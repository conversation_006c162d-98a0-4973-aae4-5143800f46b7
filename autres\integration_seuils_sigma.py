#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INTÉGRATION DES SEUILS SIGMA DANS ANALYSE_COMPLETE_AVEC_DIFF.PY

Ce script remplace les seuils arbitraires par les nouveaux seuils σ équilibrés
basés sur l'analyse statistique des métriques entropiques.

Objectif : Révolutionner l'analyse avec des seuils mathématiquement fondés
"""

import json
import re

def charger_seuils_sigma():
    """
    Charge les seuils σ calculés précédemment
    
    Returns:
        dict: Seuils σ par métrique
    """
    try:
        with open('statistiques_metriques.json', 'r', encoding='utf-8') as f:
            statistiques = json.load(f)
        
        with open('conditions_sigma_detaillees.json', 'r', encoding='utf-8') as f:
            conditions = json.load(f)
            
        return statistiques, conditions
    except Exception as e:
        print(f"❌ Erreur chargement seuils σ: {e}")
        return None, None

def generer_nouveaux_seuils_diff(stats_diff):
    """
    Génère les nouveaux seuils DIFF basés sur σ
    
    Args:
        stats_diff: Statistiques DIFF
        
    Returns:
        dict: Nouveaux seuils DIFF
    """
    moyenne = stats_diff['moyenne']
    sigma = stats_diff['ecart_type']
    
    # Nouveaux seuils basés sur σ (remplacent les seuils arbitraires)
    nouveaux_seuils = {
        'SIGNAL_PARFAIT_S': moyenne + 2.5 * sigma,      # 69.1% S (remplace < 0.020)
        'SIGNAL_EXCELLENT_S': moyenne + 2.0 * sigma,    # 67.3% S (remplace < 0.030)
        'SIGNAL_TRÈS_BON_S': moyenne + 1.5 * sigma,     # 66.8% S (remplace < 0.050)
        'SIGNAL_BON_S': moyenne + 1.0 * sigma,          # 62.5% S (remplace < 0.075)
        
        'SIGNAL_PARFAIT_O': moyenne - 1.0 * sigma,      # 52.3% O (nouveau)
        'SIGNAL_EXCELLENT_O': moyenne,                   # 53.0% O (nouveau)
        'SIGNAL_TRÈS_BON_O': moyenne + 0.5 * sigma,     # 53.0% O (nouveau)
        
        'SEUIL_DOUTEUX': moyenne + 3.0 * sigma          # Remplace > 0.150
    }
    
    return nouveaux_seuils

def generer_nouveaux_seuils_renyi(stats_renyi):
    """
    Génère les nouveaux seuils DIFF_RENYI basés sur σ
    """
    moyenne = stats_renyi['moyenne']
    sigma = stats_renyi['ecart_type']
    
    nouveaux_seuils = {
        'SIGNAL_PARFAIT_S': moyenne + 3.0 * sigma,      # 65.5% S
        'SIGNAL_EXCELLENT_S': moyenne + 2.0 * sigma,    # 61.2% S
        'SIGNAL_TRÈS_BON_S': moyenne + 1.5 * sigma,     # 60.9% S
        'SIGNAL_BON_S': moyenne + 1.0 * sigma,          # 61.5% S
        
        'SIGNAL_PARFAIT_O': moyenne - 1.0 * sigma,      # 52.2% O
        'SIGNAL_EXCELLENT_O': moyenne,                   # 52.3% O
        'SIGNAL_TRÈS_BON_O': moyenne + 0.5 * sigma,     # 53.7% O
        
        'SEUIL_DOUTEUX': moyenne + 3.5 * sigma
    }
    
    return nouveaux_seuils

def generer_nouveaux_seuils_topo(stats_topo):
    """
    Génère les nouveaux seuils DIFF_TOPO basés sur σ
    """
    moyenne = stats_topo['moyenne']
    sigma = stats_topo['ecart_type']
    
    nouveaux_seuils = {
        'SIGNAL_PARFAIT_S': moyenne - 2.0 * sigma,      # 100.0% S (valeurs très basses)
        'SIGNAL_EXCELLENT_S': moyenne + 2.0 * sigma,    # 69.0% S (valeurs très hautes)
        'SIGNAL_TRÈS_BON_S': moyenne + 2.5 * sigma,     # 67.5% S
        'SIGNAL_BON_S': moyenne + 3.0 * sigma,          # 67.1% S
        
        # DIFF_TOPO n'a pas de conditions O significatives
        'SEUIL_DOUTEUX': moyenne + 4.0 * sigma
    }
    
    return nouveaux_seuils

def generer_code_remplacement():
    """
    Génère le code de remplacement pour analyse_complete_avec_diff.py
    """
    statistiques, conditions = charger_seuils_sigma()
    if not statistiques or not conditions:
        return None
    
    # Calculer les nouveaux seuils
    seuils_diff = generer_nouveaux_seuils_diff(statistiques['DIFF'])
    seuils_renyi = generer_nouveaux_seuils_renyi(statistiques['DIFF_RENYI'])
    seuils_topo = generer_nouveaux_seuils_topo(statistiques['DIFF_TOPO'])
    
    print("🔧 NOUVEAUX SEUILS SIGMA CALCULÉS")
    print("=" * 40)
    
    print(f"\n📊 DIFF (Shannon):")
    print(f"   SIGNAL_PARFAIT_S: >= {seuils_diff['SIGNAL_PARFAIT_S']:.6f}")
    print(f"   SIGNAL_EXCELLENT_S: >= {seuils_diff['SIGNAL_EXCELLENT_S']:.6f}")
    print(f"   SIGNAL_PARFAIT_O: < {seuils_diff['SIGNAL_PARFAIT_O']:.6f}")
    print(f"   SIGNAL_EXCELLENT_O: < {seuils_diff['SIGNAL_EXCELLENT_O']:.6f}")
    
    print(f"\n📊 DIFF_RENYI (Collision):")
    print(f"   SIGNAL_PARFAIT_S: >= {seuils_renyi['SIGNAL_PARFAIT_S']:.6f}")
    print(f"   SIGNAL_EXCELLENT_S: >= {seuils_renyi['SIGNAL_EXCELLENT_S']:.6f}")
    print(f"   SIGNAL_PARFAIT_O: < {seuils_renyi['SIGNAL_PARFAIT_O']:.6f}")
    print(f"   SIGNAL_EXCELLENT_O: < {seuils_renyi['SIGNAL_EXCELLENT_O']:.6f}")
    
    print(f"\n📊 DIFF_TOPO (Topologique):")
    print(f"   SIGNAL_PARFAIT_S (bas): < {seuils_topo['SIGNAL_PARFAIT_S']:.6f}")
    print(f"   SIGNAL_EXCELLENT_S (haut): >= {seuils_topo['SIGNAL_EXCELLENT_S']:.6f}")
    
    # Générer le code de remplacement
    code_remplacement = f"""
# ============================================================================
# SEUILS BASÉS SUR ÉCART-TYPE (SIGMA) - RÉVOLUTION ENTROPIQUE
# ============================================================================
# Remplace les seuils arbitraires par des seuils statistiquement fondés
# Basé sur l'analyse de 55,328 points de données par métrique

# SEUILS DIFF (Shannon Entropy)
SEUILS_DIFF_SIGMA = {{
    'SIGNAL_PARFAIT_S': {seuils_diff['SIGNAL_PARFAIT_S']:.6f},      # 69.1% S (moyenne + 2.5σ)
    'SIGNAL_EXCELLENT_S': {seuils_diff['SIGNAL_EXCELLENT_S']:.6f},    # 67.3% S (moyenne + 2σ)
    'SIGNAL_TRÈS_BON_S': {seuils_diff['SIGNAL_TRÈS_BON_S']:.6f},     # 66.8% S (moyenne + 1.5σ)
    'SIGNAL_BON_S': {seuils_diff['SIGNAL_BON_S']:.6f},          # 62.5% S (moyenne + 1σ)
    
    'SIGNAL_PARFAIT_O': {seuils_diff['SIGNAL_PARFAIT_O']:.6f},      # 52.3% O (moyenne - 1σ)
    'SIGNAL_EXCELLENT_O': {seuils_diff['SIGNAL_EXCELLENT_O']:.6f},                   # 53.0% O (moyenne)
    'SIGNAL_TRÈS_BON_O': {seuils_diff['SIGNAL_TRÈS_BON_O']:.6f},     # 53.0% O (moyenne + 0.5σ)
    
    'SEUIL_DOUTEUX': {seuils_diff['SEUIL_DOUTEUX']:.6f}          # Remplace > 0.150
}}

# SEUILS DIFF_RENYI (Collision Entropy)
SEUILS_RENYI_SIGMA = {{
    'SIGNAL_PARFAIT_S': {seuils_renyi['SIGNAL_PARFAIT_S']:.6f},      # 65.5% S (moyenne + 3σ)
    'SIGNAL_EXCELLENT_S': {seuils_renyi['SIGNAL_EXCELLENT_S']:.6f},    # 61.2% S (moyenne + 2σ)
    'SIGNAL_TRÈS_BON_S': {seuils_renyi['SIGNAL_TRÈS_BON_S']:.6f},     # 60.9% S (moyenne + 1.5σ)
    'SIGNAL_BON_S': {seuils_renyi['SIGNAL_BON_S']:.6f},          # 61.5% S (moyenne + 1σ)
    
    'SIGNAL_PARFAIT_O': {seuils_renyi['SIGNAL_PARFAIT_O']:.6f},      # 52.2% O (moyenne - 1σ)
    'SIGNAL_EXCELLENT_O': {seuils_renyi['SIGNAL_EXCELLENT_O']:.6f},                   # 52.3% O (moyenne)
    'SIGNAL_TRÈS_BON_O': {seuils_renyi['SIGNAL_TRÈS_BON_O']:.6f},     # 53.7% O (moyenne + 0.5σ)
    
    'SEUIL_DOUTEUX': {seuils_renyi['SEUIL_DOUTEUX']:.6f}
}}

# SEUILS DIFF_TOPO (Topological Entropy)
SEUILS_TOPO_SIGMA = {{
    'SIGNAL_PARFAIT_S_BAS': {seuils_topo['SIGNAL_PARFAIT_S']:.6f},      # 100.0% S (moyenne - 2σ)
    'SIGNAL_EXCELLENT_S_HAUT': {seuils_topo['SIGNAL_EXCELLENT_S']:.6f},    # 69.0% S (moyenne + 2σ)
    'SIGNAL_TRÈS_BON_S': {seuils_topo['SIGNAL_TRÈS_BON_S']:.6f},     # 67.5% S (moyenne + 2.5σ)
    'SIGNAL_BON_S': {seuils_topo['SIGNAL_BON_S']:.6f},          # 67.1% S (moyenne + 3σ)
    
    'SEUIL_DOUTEUX': {seuils_topo['SEUIL_DOUTEUX']:.6f}
}}

def analyser_tranche_sigma(donnees, nom_condition, conditions_s, conditions_o, metrique='DIFF'):
    \"\"\"
    Analyse une tranche avec les nouveaux seuils σ équilibrés
    
    Args:
        donnees: Données à analyser
        nom_condition: Nom de la condition
        conditions_s: Liste des conditions S
        conditions_o: Liste des conditions O
        metrique: Type de métrique ('DIFF', 'RENYI', 'TOPO')
    \"\"\"
    if len(donnees) < 100:  # Seuil minimum
        return
    
    patterns_s = [d for d in donnees if d.get('pattern', d.get('pattern_so', '')) == 'S']
    patterns_o = [d for d in donnees if d.get('pattern', d.get('pattern_so', '')) == 'O']
    
    total = len(donnees)
    nb_s = len(patterns_s)
    nb_o = len(patterns_o)
    
    if total == 0:
        return
    
    pourcentage_s = (nb_s / total) * 100
    pourcentage_o = (nb_o / total) * 100
    
    # Seuils adaptatifs selon la métrique et le pattern
    if metrique == 'DIFF':
        seuil_s = 55.0  # DIFF a des conditions S fortes
        seuil_o = 52.0  # DIFF a des conditions O modérées
    elif metrique == 'RENYI':
        seuil_s = 55.0  # DIFF_RENYI similaire à DIFF
        seuil_o = 52.0
    elif metrique == 'TOPO':
        seuil_s = 55.0  # DIFF_TOPO très biaisé S
        seuil_o = 60.0  # Seuil très élevé car TOPO n'a pas de conditions O
    else:
        seuil_s = 55.0
        seuil_o = 52.0
    
    condition_data = {{
        'nom': nom_condition,
        'total_cas': total,
        'nb_s': nb_s,
        'nb_o': nb_o,
        'pourcentage_s': pourcentage_s,
        'pourcentage_o': pourcentage_o,
        'force': 'FORTE' if max(pourcentage_s, pourcentage_o) >= 65 else 'MODÉRÉE' if max(pourcentage_s, pourcentage_o) >= 55 else 'FAIBLE',
        'metrique': metrique
    }}
    
    # Ajouter aux conditions appropriées avec seuils adaptatifs
    if pourcentage_s >= seuil_s and pourcentage_s > pourcentage_o:
        conditions_s.append(condition_data)
    elif pourcentage_o >= seuil_o and pourcentage_o > pourcentage_s:
        conditions_o.append(condition_data)
"""
    
    return code_remplacement, seuils_diff, seuils_renyi, seuils_topo

def generer_nouvelles_tranches_diff(seuils_diff):
    """
    Génère les nouvelles tranches DIFF basées sur σ
    """
    code_tranches = f"""
    # NOUVELLES TRANCHES DIFF BASÉES SUR SIGMA
    tranches_diff_sigma = [
        # Conditions favorisant S (valeurs élevées)
        (SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_S'], 10.0, "SIGNAL_PARFAIT_S"),        # >= 2.5σ : 69.1% S
        (SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_S'], SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_S'], "SIGNAL_EXCELLENT_S"),    # 2σ-2.5σ : 67.3% S
        (SEUILS_DIFF_SIGMA['SIGNAL_TRÈS_BON_S'], SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_S'], "SIGNAL_TRÈS_BON_S"),     # 1.5σ-2σ : 66.8% S
        (SEUILS_DIFF_SIGMA['SIGNAL_BON_S'], SEUILS_DIFF_SIGMA['SIGNAL_TRÈS_BON_S'], "SIGNAL_BON_S"),          # 1σ-1.5σ : 62.5% S
        
        # Conditions favorisant O (valeurs moyennes/basses)
        (SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_O'], SEUILS_DIFF_SIGMA['SIGNAL_TRÈS_BON_O'], "SIGNAL_EXCELLENT_O"),                   # moyenne-moyenne+0.5σ : 53.0% O
        (SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_O'], SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_O'], "SIGNAL_PARFAIT_O"),      # moyenne-1σ à moyenne : 52.3% O
        (0.0, SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_O'], "SIGNAL_TRÈS_PARFAIT_O"),      # < moyenne-1σ : Conditions O très fortes
        
        # Zone neutre/douteux
        (SEUILS_DIFF_SIGMA['SEUIL_DOUTEUX'], 10.0, "SIGNAL_DOUTEUX")       # > 3σ : Abstention
    ]
    
    for min_val, max_val, nom in tranches_diff_sigma:
        donnees_tranche = [d for d in donnees if min_val <= d['diff'] < max_val]
        if len(donnees_tranche) >= 100:
            analyser_tranche_sigma(donnees_tranche, f"DIFF_{{nom}}", conditions_s, conditions_o, 'DIFF')
"""
    return code_tranches

def main():
    """
    Fonction principale d'intégration des seuils σ
    """
    print("🚀 INTÉGRATION SEUILS SIGMA")
    print("=" * 30)
    
    # Générer le code de remplacement
    code_remplacement, seuils_diff, seuils_renyi, seuils_topo = generer_code_remplacement()
    if not code_remplacement:
        return
    
    # Générer les nouvelles tranches
    nouvelles_tranches_diff = generer_nouvelles_tranches_diff(seuils_diff)
    
    # Sauvegarder le code complet
    code_complet = code_remplacement + nouvelles_tranches_diff
    
    with open('code_integration_sigma.py', 'w', encoding='utf-8') as f:
        f.write(code_complet)
    
    print("✅ code_integration_sigma.py généré")
    
    # Sauvegarder les seuils pour référence
    seuils_complets = {
        'DIFF': seuils_diff,
        'DIFF_RENYI': seuils_renyi,
        'DIFF_TOPO': seuils_topo
    }
    
    with open('seuils_sigma_finaux.json', 'w', encoding='utf-8') as f:
        json.dump(seuils_complets, f, indent=2, ensure_ascii=False)
    
    print("✅ seuils_sigma_finaux.json sauvegardé")
    
    print("\n🎯 PROCHAINES ÉTAPES:")
    print("1. Intégrer le code dans analyse_complete_avec_diff.py")
    print("2. Remplacer les tranches arbitraires par les tranches σ")
    print("3. Tester avec les nouveaux seuils équilibrés")
    print("4. Valider les performances prédictives")

if __name__ == "__main__":
    main()
