#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSE SPÉCIFIQUE DES PATTERNS O - TERRITOIRES ENTROPIQUES

Ce script analyse spécifiquement les patterns O pour identifier leurs "territoires"
dans l'espace des métriques entropiques et établir les limites où O domine.

Objectif : Équilibrer l'analyse S/O et découvrir les conditions favorisant O
"""

import json
import numpy as np
import pandas as pd
from datetime import datetime

def charger_donnees_patterns():
    """
    Charge les données brutes et sépare S et O
    
    Returns:
        dict: Données séparées par pattern et métrique
    """
    try:
        with open('donnees_brutes_metriques.json', 'r', encoding='utf-8') as f:
            donnees_brutes = json.load(f)
        
        with open('statistiques_metriques.json', 'r', encoding='utf-8') as f:
            statistiques = json.load(f)
            
        return donnees_brutes, statistiques
    except Exception as e:
        print(f"❌ Erreur chargement données: {e}")
        return None, None

def extraire_donnees_par_pattern(donnees_brutes):
    """
    Extrait et structure les données par pattern S/O pour chaque métrique
    
    Args:
        donnees_brutes: Données brutes par métrique
        
    Returns:
        dict: Structure {métrique: {S: [...], O: [...]}}
    """
    print("🔍 EXTRACTION DONNÉES PAR PATTERN S/O")
    print("=" * 40)
    
    donnees_par_pattern = {}
    
    for nom_metrique, donnees in donnees_brutes.items():
        print(f"\n📊 {nom_metrique}:")
        
        # Extraire valeurs et patterns selon la métrique
        if nom_metrique == 'DIFF':
            valeurs_patterns = [(d.get('diff', 0), d.get('pattern', ''), d) for d in donnees 
                              if d.get('diff') is not None and d.get('pattern') in ['S', 'O']]
        elif nom_metrique == 'DIFF_RENYI':
            valeurs_patterns = [(d.get('diff_renyi', 0), d.get('pattern_so', ''), d) for d in donnees 
                              if d.get('diff_renyi') is not None and d.get('pattern_so') in ['S', 'O']]
        elif nom_metrique == 'DIFF_TOPO':
            valeurs_patterns = [(d.get('diff_topo', 0), d.get('pattern_so', ''), d) for d in donnees 
                              if d.get('diff_topo') is not None and d.get('pattern_so') in ['S', 'O']]
        
        # Séparer S et O
        donnees_s = [d for v, p, d in valeurs_patterns if p == 'S']
        donnees_o = [d for v, p, d in valeurs_patterns if p == 'O']
        
        donnees_par_pattern[nom_metrique] = {
            'S': donnees_s,
            'O': donnees_o
        }
        
        print(f"   S: {len(donnees_s)} cas")
        print(f"   O: {len(donnees_o)} cas")
        print(f"   Ratio S/O: {len(donnees_s)/len(donnees_o):.2f}" if len(donnees_o) > 0 else "   Ratio S/O: ∞")
    
    return donnees_par_pattern

def analyser_distributions_o(donnees_par_pattern, statistiques):
    """
    Analyse détaillée des distributions des patterns O
    
    Args:
        donnees_par_pattern: Données séparées par pattern
        statistiques: Statistiques des métriques
        
    Returns:
        dict: Analyses des patterns O
    """
    print("\n🎯 ANALYSE DISTRIBUTIONS PATTERNS O")
    print("=" * 40)
    
    analyses_o = {}
    
    for nom_metrique in ['DIFF', 'DIFF_RENYI', 'DIFF_TOPO']:
        if nom_metrique not in donnees_par_pattern:
            continue
            
        print(f"\n🔍 {nom_metrique} - PATTERNS O:")
        
        donnees_o = donnees_par_pattern[nom_metrique]['O']
        stats = statistiques[nom_metrique]
        
        if not donnees_o:
            print("   ❌ Aucune donnée O")
            continue
        
        # Extraire valeurs O selon la métrique
        if nom_metrique == 'DIFF':
            valeurs_o = [d.get('diff', 0) for d in donnees_o]
        elif nom_metrique == 'DIFF_RENYI':
            valeurs_o = [d.get('diff_renyi', 0) for d in donnees_o]
        elif nom_metrique == 'DIFF_TOPO':
            valeurs_o = [d.get('diff_topo', 0) for d in donnees_o]
        
        valeurs_o = np.array(valeurs_o)
        
        # Statistiques spécifiques aux O
        stats_o = {
            'count': len(valeurs_o),
            'moyenne_o': np.mean(valeurs_o),
            'mediane_o': np.median(valeurs_o),
            'ecart_type_o': np.std(valeurs_o),
            'variance_o': np.var(valeurs_o),
            'min_o': np.min(valeurs_o),
            'max_o': np.max(valeurs_o),
            'q25_o': np.percentile(valeurs_o, 25),
            'q75_o': np.percentile(valeurs_o, 75)
        }
        
        # Comparaison avec statistiques globales
        stats_o['ecart_moyenne_globale'] = stats_o['moyenne_o'] - stats['moyenne']
        stats_o['ratio_ecart_type'] = stats_o['ecart_type_o'] / stats['ecart_type']
        
        analyses_o[nom_metrique] = stats_o
        
        # Affichage
        print(f"   📊 Nombre O: {stats_o['count']}")
        print(f"   📊 Moyenne O: {stats_o['moyenne_o']:.6f} (globale: {stats['moyenne']:.6f})")
        print(f"   📊 Écart vs globale: {stats_o['ecart_moyenne_globale']:.6f}")
        print(f"   📊 Écart-type O: {stats_o['ecart_type_o']:.6f}")
        print(f"   📊 Min/Max O: {stats_o['min_o']:.6f} / {stats_o['max_o']:.6f}")
        print(f"   📊 Q25/Q75 O: {stats_o['q25_o']:.6f} / {stats_o['q75_o']:.6f}")
    
    return analyses_o

def identifier_territoires_o(donnees_par_pattern, statistiques):
    """
    Identifie les "territoires" où les patterns O dominent
    
    Args:
        donnees_par_pattern: Données séparées par pattern
        statistiques: Statistiques des métriques
        
    Returns:
        dict: Territoires O identifiés
    """
    print("\n🗺️ IDENTIFICATION TERRITOIRES PATTERNS O")
    print("=" * 45)
    
    territoires_o = {}
    
    for nom_metrique in ['DIFF', 'DIFF_RENYI', 'DIFF_TOPO']:
        if nom_metrique not in donnees_par_pattern:
            continue
            
        print(f"\n🎯 {nom_metrique} - TERRITOIRES O:")
        
        donnees_s = donnees_par_pattern[nom_metrique]['S']
        donnees_o = donnees_par_pattern[nom_metrique]['O']
        stats = statistiques[nom_metrique]
        
        # Extraire valeurs selon la métrique
        if nom_metrique == 'DIFF':
            valeurs_s = [d.get('diff', 0) for d in donnees_s]
            valeurs_o = [d.get('diff', 0) for d in donnees_o]
        elif nom_metrique == 'DIFF_RENYI':
            valeurs_s = [d.get('diff_renyi', 0) for d in donnees_s]
            valeurs_o = [d.get('diff_renyi', 0) for d in donnees_o]
        elif nom_metrique == 'DIFF_TOPO':
            valeurs_s = [d.get('diff_topo', 0) for d in donnees_s]
            valeurs_o = [d.get('diff_topo', 0) for d in donnees_o]
        
        territoires_metrique = []
        
        # Analyser différentes plages de valeurs
        plages = [
            ('TRÈS_BAS', 0, stats['moyenne'] - 2*stats['ecart_type']),
            ('BAS', stats['moyenne'] - 2*stats['ecart_type'], stats['moyenne'] - stats['ecart_type']),
            ('MOYEN_BAS', stats['moyenne'] - stats['ecart_type'], stats['moyenne']),
            ('MOYEN_HAUT', stats['moyenne'], stats['moyenne'] + stats['ecart_type']),
            ('HAUT', stats['moyenne'] + stats['ecart_type'], stats['moyenne'] + 2*stats['ecart_type']),
            ('TRÈS_HAUT', stats['moyenne'] + 2*stats['ecart_type'], float('inf'))
        ]
        
        for nom_plage, min_val, max_val in plages:
            # Compter S et O dans cette plage
            s_dans_plage = len([v for v in valeurs_s if min_val <= v < max_val])
            o_dans_plage = len([v for v in valeurs_o if min_val <= v < max_val])
            
            total_plage = s_dans_plage + o_dans_plage
            
            if total_plage >= 100:  # Seuil minimum pour analyse
                pct_o = (o_dans_plage / total_plage) * 100
                pct_s = (s_dans_plage / total_plage) * 100
                
                # Identifier territoires où O domine (> 52% pour être plus inclusif)
                if pct_o > 52:
                    territoires_metrique.append({
                        'nom': f'{nom_metrique}_{nom_plage}',
                        'description': f'{nom_metrique} dans [{min_val:.6f}, {max_val:.6f})',
                        'min_val': min_val,
                        'max_val': max_val,
                        'total_cas': total_plage,
                        'count_s': s_dans_plage,
                        'count_o': o_dans_plage,
                        'pourcentage_o': pct_o,
                        'pourcentage_s': pct_s,
                        'dominance_o': pct_o - 50  # Écart par rapport à l'équilibre
                    })
                    
                    print(f"   🎯 {nom_plage}: {pct_o:.1f}% O ({total_plage} cas)")
                    print(f"      Plage: [{min_val:.6f}, {max_val:.6f})")
                    print(f"      Dominance O: +{pct_o-50:.1f}%")
        
        territoires_o[nom_metrique] = territoires_metrique
        
        if not territoires_metrique:
            print("   ❌ Aucun territoire O dominant identifié")
    
    return territoires_o

def analyser_combinaisons_o(donnees_par_pattern):
    """
    Analyse les combinaisons de conditions favorisant O
    
    Args:
        donnees_par_pattern: Données séparées par pattern
        
    Returns:
        dict: Combinaisons favorisant O
    """
    print("\n🔗 ANALYSE COMBINAISONS FAVORISANT O")
    print("=" * 40)
    
    combinaisons_o = {}
    
    # Analyser les ratios L4/L5 pour les patterns O
    for nom_metrique in ['DIFF', 'DIFF_RENYI', 'DIFF_TOPO']:
        if nom_metrique not in donnees_par_pattern:
            continue
            
        print(f"\n🔍 {nom_metrique} - COMBINAISONS O:")
        
        donnees_o = donnees_par_pattern[nom_metrique]['O']
        
        if not donnees_o:
            continue
        
        # Extraire ratios L4/L5 pour les cas O
        if nom_metrique == 'DIFF':
            ratios_l4_o = [d.get('ratio_l4', 0) for d in donnees_o if d.get('ratio_l4') is not None]
            ratios_l5_o = [d.get('ratio_l5', 0) for d in donnees_o if d.get('ratio_l5') is not None]
        elif nom_metrique == 'DIFF_RENYI':
            ratios_l4_o = [d.get('ratio_l4_renyi', 0) for d in donnees_o if d.get('ratio_l4_renyi') is not None]
            ratios_l5_o = [d.get('ratio_l5_renyi', 0) for d in donnees_o if d.get('ratio_l5_renyi') is not None]
        elif nom_metrique == 'DIFF_TOPO':
            ratios_l4_o = [d.get('ratio_l4_topo', 0) for d in donnees_o if d.get('ratio_l4_topo') is not None]
            ratios_l5_o = [d.get('ratio_l5_topo', 0) for d in donnees_o if d.get('ratio_l5_topo') is not None]
        
        if ratios_l4_o and ratios_l5_o:
            stats_ratios_o = {
                'moyenne_l4_o': np.mean(ratios_l4_o),
                'moyenne_l5_o': np.mean(ratios_l5_o),
                'ecart_type_l4_o': np.std(ratios_l4_o),
                'ecart_type_l5_o': np.std(ratios_l5_o),
                'min_l4_o': np.min(ratios_l4_o),
                'max_l4_o': np.max(ratios_l4_o),
                'min_l5_o': np.min(ratios_l5_o),
                'max_l5_o': np.max(ratios_l5_o)
            }
            
            combinaisons_o[nom_metrique] = stats_ratios_o
            
            print(f"   📊 Ratio L4 O: {stats_ratios_o['moyenne_l4_o']:.6f} ± {stats_ratios_o['ecart_type_l4_o']:.6f}")
            print(f"   📊 Ratio L5 O: {stats_ratios_o['moyenne_l5_o']:.6f} ± {stats_ratios_o['ecart_type_l5_o']:.6f}")
            print(f"   📊 Plage L4 O: [{stats_ratios_o['min_l4_o']:.6f}, {stats_ratios_o['max_l4_o']:.6f}]")
            print(f"   📊 Plage L5 O: [{stats_ratios_o['min_l5_o']:.6f}, {stats_ratios_o['max_l5_o']:.6f}]")
    
    return combinaisons_o

def generer_rapport_patterns_o(analyses_o, territoires_o, combinaisons_o):
    """
    Génère un rapport complet sur les patterns O
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    nom_fichier = f"rapport_patterns_o_{timestamp}.txt"
    
    with open(nom_fichier, 'w', encoding='utf-8') as f:
        f.write("ANALYSE SPÉCIFIQUE DES PATTERNS O - TERRITOIRES ENTROPIQUES\n")
        f.write("=" * 70 + "\n")
        f.write(f"Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("RÉSUMÉ EXÉCUTIF\n")
        f.write("-" * 20 + "\n")
        f.write("• Analyse équilibrée des patterns S ET O\n")
        f.write("• Identification des territoires où O domine\n")
        f.write("• Caractérisation des conditions favorisant O\n")
        f.write("• Équilibrage de l'analyse entropique\n\n")
        
        f.write("DISTRIBUTIONS PATTERNS O\n")
        f.write("-" * 30 + "\n")
        for nom_metrique, stats_o in analyses_o.items():
            f.write(f"\n{nom_metrique} - PATTERNS O:\n")
            f.write(f"  Nombre de cas O: {stats_o['count']}\n")
            f.write(f"  Moyenne O: {stats_o['moyenne_o']:.6f}\n")
            f.write(f"  Écart vs moyenne globale: {stats_o['ecart_moyenne_globale']:.6f}\n")
            f.write(f"  Écart-type O: {stats_o['ecart_type_o']:.6f}\n")
            f.write(f"  Plage O: [{stats_o['min_o']:.6f}, {stats_o['max_o']:.6f}]\n")
        
        f.write("\n\nTERRITOIRES DOMINÉS PAR O\n")
        f.write("-" * 30 + "\n")
        for nom_metrique, territoires in territoires_o.items():
            f.write(f"\n{nom_metrique}:\n")
            if territoires:
                for territoire in territoires:
                    f.write(f"  • {territoire['nom']}: {territoire['pourcentage_o']:.1f}% O\n")
                    f.write(f"    Plage: [{territoire['min_val']:.6f}, {territoire['max_val']:.6f})\n")
                    f.write(f"    Cas: {territoire['total_cas']} (S:{territoire['count_s']}, O:{territoire['count_o']})\n")
                    f.write(f"    Dominance O: +{territoire['dominance_o']:.1f}%\n\n")
            else:
                f.write("  Aucun territoire O dominant identifié\n")
        
        f.write("\nCOMBINAISONS RATIOS FAVORISANT O\n")
        f.write("-" * 40 + "\n")
        for nom_metrique, stats_ratios in combinaisons_o.items():
            f.write(f"\n{nom_metrique}:\n")
            f.write(f"  Ratio L4 moyen (O): {stats_ratios['moyenne_l4_o']:.6f}\n")
            f.write(f"  Ratio L5 moyen (O): {stats_ratios['moyenne_l5_o']:.6f}\n")
            f.write(f"  Plage L4 (O): [{stats_ratios['min_l4_o']:.6f}, {stats_ratios['max_l4_o']:.6f}]\n")
            f.write(f"  Plage L5 (O): [{stats_ratios['min_l5_o']:.6f}, {stats_ratios['max_l5_o']:.6f}]\n")
    
    print(f"✅ Rapport patterns O généré: {nom_fichier}")
    return nom_fichier

def main():
    """
    Fonction principale d'analyse des patterns O
    """
    print("🎯 ANALYSE SPÉCIFIQUE PATTERNS O")
    print("=" * 40)
    
    # Charger données
    donnees_brutes, statistiques = charger_donnees_patterns()
    if not donnees_brutes or not statistiques:
        return
    
    # Extraire données par pattern
    donnees_par_pattern = extraire_donnees_par_pattern(donnees_brutes)
    
    # Analyser distributions O
    analyses_o = analyser_distributions_o(donnees_par_pattern, statistiques)
    
    # Identifier territoires O
    territoires_o = identifier_territoires_o(donnees_par_pattern, statistiques)
    
    # Analyser combinaisons O
    combinaisons_o = analyser_combinaisons_o(donnees_par_pattern)
    
    # Générer rapport
    nom_rapport = generer_rapport_patterns_o(analyses_o, territoires_o, combinaisons_o)
    
    # Sauvegarder données structurées
    donnees_o_completes = {
        'analyses_o': analyses_o,
        'territoires_o': territoires_o,
        'combinaisons_o': combinaisons_o
    }
    
    with open('analyse_patterns_o_complete.json', 'w', encoding='utf-8') as f:
        json.dump(donnees_o_completes, f, indent=2, ensure_ascii=False)
    print("✅ analyse_patterns_o_complete.json sauvegardé")
    
    print("\n🎉 ANALYSE PATTERNS O TERMINÉE")
    print(f"📊 Consultez {nom_rapport} pour les résultats détaillés")

if __name__ == "__main__":
    main()
