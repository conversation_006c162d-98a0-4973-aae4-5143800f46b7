#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST DE LA DÉTECTION AUTOMATIQUE DU DATASET
===========================================

Test de la fonction detecter_dataset_le_plus_recent() 
pour vérifier qu'elle fonctionne correctement.
"""

import sys
import os

# Ajouter le répertoire courant au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importer la fonction de détection depuis le fichier principal
from analyse_complete_avec_diff import detecter_dataset_le_plus_recent

def test_detection_automatique():
    """
    Test de la fonction de détection automatique du dataset
    """
    print("🧪 TEST DE LA DÉTECTION AUTOMATIQUE DU DATASET")
    print("=" * 60)
    
    # Tester la détection
    fichier_detecte, nombre_parties = detecter_dataset_le_plus_recent()
    
    if fichier_detecte:
        print(f"\n✅ TEST RÉUSSI !")
        print(f"   📄 Fichier détecté: {fichier_detecte}")
        print(f"   🔢 Nombre de parties: {nombre_parties:,}")
        print(f"   📊 Taille: {os.path.getsize(fichier_detecte) / (1024**3):.2f} GB")
        
        # Vérifier que le fichier existe vraiment
        if os.path.exists(fichier_detecte):
            print(f"   ✅ Fichier vérifié: existe bien")
        else:
            print(f"   ❌ Erreur: fichier n'existe pas")
            
        return True
    else:
        print(f"\n❌ TEST ÉCHOUÉ !")
        print(f"   Aucun fichier détecté")
        return False

if __name__ == "__main__":
    success = test_detection_automatique()
    
    if success:
        print(f"\n🎯 CONCLUSION: La détection automatique fonctionne parfaitement")
        print(f"   Le programme analyse_complete_avec_diff.py utilisera automatiquement")
        print(f"   le dataset le plus récent et traitera TOUTES les parties")
    else:
        print(f"\n⚠️ CONCLUSION: Problème avec la détection automatique")
        print(f"   Vérifiez la présence de fichiers dataset_baccarat_lupasco_*.json")
