#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IMPLÉMENTEUR DE MODULE INDÉPENDANT
==================================

Extrait les méthodes des fichiers texte et génère un module Python
indépendant pour remplacer analyseur_transitions_index5.py

OBJECTIF: Créer module_entropie_independant.py avec toutes les classes nécessaires
"""

import os
import re
from datetime import datetime

class ImplementeurModuleIndependant:
    """
    Implémenteur qui génère un module Python indépendant
    à partir des fichiers texte d'extraction
    """
    
    def __init__(self, dossier_extraction="extraction_methodes_20250627_233347"):
        self.dossier_extraction = dossier_extraction
        self.methodes_extraites = {}
        self.imports_necessaires = set()
        
        # Configuration des classes à extraire (selon votre demande)
        self.classes_cibles = {
            'AnalyseurEvolutionEntropique': {
                'fichier': 'analyseurevolutionentropique_methodes.txt',
                'methodes': [
                    '__init__',
                    'analyser_toutes_parties_entropiques',
                    'analyser_partie_entropique',
                    '_traiter_chunk_parties',
                    '_calculer_stats_partie',
                    '_calculer_analyses_globales',
                    'generer_rapport_evolution_entropique',
                    '_ecrire_analyses_globales',
                    '_generer_signatures_integrees',
                    'initialiser_bases_signatures',
                    '_calculer_entropie_shannon',
                    '_classifier_ratio',
                    '_compter_categories',
                    '_analyser_distribution',
                    '_identifier_zones_predictibles'
                ]
            },
            'AnalyseurEvolutionRatios': {
                'fichier': 'analyseurevolutionratios_methodes.txt',
                'methodes': [
                    '__init__',
                    'analyser_evolution_toutes_parties',
                    '_analyser_evolution_partie',
                    '_analyser_tendance',
                    '_detecter_oscillations',
                    '_analyser_convergence',
                    '_extraire_index3_depuis_index5',
                    '_calculer_variations_ratios',
                    '_calculer_patterns_soe',
                    '_calculer_correlation',
                    '_classifier_pattern_evolution',
                    '_identifier_patterns_evolution',
                    '_calculer_statistiques_evolution',
                    'generer_rapport_evolution_ratios',
                    'generer_rapport_evolution_complete',
                    '_ecrire_resume_executif',
                    '_ecrire_patterns_evolution',
                    '_ecrire_statistiques_globales',
                    '_ecrire_analyses_detaillees',
                    '_ecrire_conclusions'
                ]
            },
            'AnalyseurTransitionsIndex5': {
                'fichier': 'analyseurtransitionsindex5_methodes.txt',
                'methodes': [
                    '__init__',
                    '_charger_avec_cache_ultra_optimise'
                ]
            }
        }
    
    def charger_methodes_depuis_fichier(self, nom_fichier):
        """
        Charge toutes les méthodes depuis un fichier texte d'extraction
        """
        chemin_fichier = os.path.join(self.dossier_extraction, nom_fichier)

        if not os.path.exists(chemin_fichier):
            print(f"❌ Fichier non trouvé: {chemin_fichier}")
            return {}

        with open(chemin_fichier, 'r', encoding='utf-8') as f:
            contenu = f.read()

        # Extraire les méthodes du fichier
        methodes = {}

        # Diviser par les séparateurs de méthodes (80 caractères '=')
        sections = contenu.split('=' * 80)

        print(f"  🔍 {len(sections)} sections trouvées dans le fichier")

        for i, section in enumerate(sections):
            if 'MÉTHODE:' in section:
                print(f"  📋 Section {i} contient une méthode")

                # Extraire le nom de la méthode
                match = re.search(r'MÉTHODE: (\w+)\.(\w+)', section)
                if match:
                    classe_nom = match.group(1)
                    methode_nom = match.group(2)
                    print(f"    🎯 Méthode trouvée: {methode_nom}")

                    # Extraire le code de la méthode
                    lignes = section.split('\n')
                    code_debut = None

                    # Trouver le début du code (ligne avec 'def ' - peut être indentée)
                    for j, ligne in enumerate(lignes):
                        if 'def ' in ligne and ligne.strip().startswith('def '):
                            code_debut = j
                            print(f"    📍 Code trouvé à la ligne {j}: {ligne.strip()[:50]}...")
                            break

                    if code_debut is not None:
                        # Extraire le code complet jusqu'à la fin de la section
                        lignes_code = lignes[code_debut:]

                        # Nettoyer les lignes vides en fin
                        while lignes_code and not lignes_code[-1].strip():
                            lignes_code.pop()

                        code_methode = '\n'.join(lignes_code)
                        methodes[methode_nom] = code_methode

                        # Identifier les imports nécessaires
                        self._identifier_imports(code_methode)

                        print(f"    ✅ {methode_nom} extraite ({len(lignes_code)} lignes)")
                    else:
                        print(f"    ❌ Code non trouvé pour {methode_nom}")
                else:
                    print(f"    ❌ Nom de méthode non extrait de la section {i}")

        print(f"✅ {len(methodes)} méthodes chargées depuis {nom_fichier}")
        return methodes
    
    def _identifier_imports(self, code_methode):
        """
        Identifie les imports nécessaires dans le code d'une méthode
        """
        # Rechercher les imports standards
        imports_standards = [
            'import os', 'import sys', 'import json', 'import math', 'import time',
            'import multiprocessing', 'import gc', 'import hashlib', 'import pickle',
            'from datetime import datetime', 'from collections import defaultdict',
            'from collections import Counter', 'import numpy as np'
        ]
        
        for import_line in imports_standards:
            module = import_line.split()[-1]
            if module in code_methode or module.replace('_', '') in code_methode:
                self.imports_necessaires.add(import_line)
        
        # Rechercher les imports spécialisés
        if 'ijson' in code_methode:
            self.imports_necessaires.add('import ijson')
        if 'orjson' in code_methode:
            self.imports_necessaires.add('import orjson')
        if 'mmap' in code_methode:
            self.imports_necessaires.add('import mmap')
        if 'numba' in code_methode or '@jit' in code_methode:
            self.imports_necessaires.add('from numba import jit, prange')
    
    def extraire_toutes_methodes(self):
        """
        Extrait toutes les méthodes de toutes les classes cibles
        """
        print(f"\n🔥 EXTRACTION DES MÉTHODES DEPUIS LES FICHIERS TEXTE")
        print("=" * 60)
        
        for nom_classe, config in self.classes_cibles.items():
            print(f"\n📊 Extraction classe: {nom_classe}")
            print("-" * 40)
            
            methodes = self.charger_methodes_depuis_fichier(config['fichier'])
            
            # Filtrer seulement les méthodes demandées
            methodes_filtrees = {}
            for nom_methode in config['methodes']:
                if nom_methode in methodes:
                    methodes_filtrees[nom_methode] = methodes[nom_methode]
                    print(f"  ✅ {nom_methode}")
                else:
                    print(f"  ❌ {nom_methode} (non trouvée)")
            
            self.methodes_extraites[nom_classe] = methodes_filtrees
            print(f"✅ {len(methodes_filtrees)}/{len(config['methodes'])} méthodes extraites")
        
        return True
    
    def generer_module_independant(self):
        """
        Génère le module Python indépendant
        """
        print(f"\n🔥 GÉNÉRATION DU MODULE INDÉPENDANT")
        print("=" * 50)
        
        nom_module = "module_entropie_independant.py"
        
        with open(nom_module, 'w', encoding='utf-8') as f:
            # En-tête du module
            f.write('#!/usr/bin/env python3\n')
            f.write('# -*- coding: utf-8 -*-\n')
            f.write('"""\n')
            f.write('MODULE ENTROPIE INDÉPENDANT\n')
            f.write('===========================\n\n')
            f.write('Module généré automatiquement pour rendre analyse_complete_avec_diff.py\n')
            f.write('complètement indépendant de analyseur_transitions_index5.py\n\n')
            f.write(f'Généré le: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
            f.write('Classes incluses:\n')
            for nom_classe, methodes in self.methodes_extraites.items():
                f.write(f'- {nom_classe}: {len(methodes)} méthodes\n')
            f.write('"""\n\n')
            
            # Imports nécessaires
            f.write('# IMPORTS NÉCESSAIRES\n')
            f.write('# ' + '=' * 30 + '\n')
            for import_line in sorted(self.imports_necessaires):
                f.write(f'{import_line}\n')
            f.write('\n')
            
            # Générer chaque classe
            for nom_classe, methodes in self.methodes_extraites.items():
                f.write(f'class {nom_classe}:\n')
                f.write(f'    """\n')
                f.write(f'    {nom_classe} - Version indépendante\n')
                f.write(f'    Extraite automatiquement depuis analyseur_transitions_index5.py\n')
                f.write(f'    """\n\n')
                
                # Générer chaque méthode
                for nom_methode, code_methode in methodes.items():
                    # Ajuster l'indentation
                    lignes_code = code_methode.split('\n')
                    for ligne in lignes_code:
                        if ligne.strip():  # Ignorer les lignes vides
                            f.write(f'    {ligne}\n')
                        else:
                            f.write('\n')
                    f.write('\n')
                
                f.write('\n')
        
        print(f"✅ Module généré: {nom_module}")
        return nom_module

def main():
    """
    Point d'entrée principal de l'implémenteur
    """
    print("🔥 IMPLÉMENTEUR DE MODULE INDÉPENDANT")
    print("=" * 50)
    print("Objectif: Créer module_entropie_independant.py")
    
    # Créer l'implémenteur
    implementeur = ImplementeurModuleIndependant()
    
    # Extraire toutes les méthodes
    if implementeur.extraire_toutes_methodes():
        # Générer le module indépendant
        nom_module = implementeur.generer_module_independant()
        
        print(f"\n✅ MODULE INDÉPENDANT CRÉÉ!")
        print(f"📁 Fichier: {nom_module}")
        print(f"🎯 Prochaine étape: Modifier analyse_complete_avec_diff.py")
        print(f"   pour utiliser ce module au lieu de analyseur_transitions_index5.py")
        
        return True
    else:
        print(f"\n❌ GÉNÉRATION ÉCHOUÉE")
        return False

if __name__ == "__main__":
    main()
