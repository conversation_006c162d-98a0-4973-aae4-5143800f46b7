#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST DE MIGRATION DIFF_TOPO
===========================

Script de test pour vérifier que la migration vers AnalyseurDiffTopo
fonctionne correctement et produit les mêmes résultats.

Auteur: Maître de l'Entropie
Date: 2025-06-27
"""

import sys
import json
from typing import List, Dict, Any

def test_migration_diff_topo():
    """
    Test complet de la migration DIFF_TOPO
    
    Vérifie que:
    1. AnalyseurDiffTopo peut être importé
    2. Les méthodes principales fonctionnent
    3. L'architecture DIFF_TOPO est identique
    4. Le multiprocessing fonctionne
    """
    print("🧪 TEST DE MIGRATION DIFF_TOPO")
    print("=" * 50)
    
    try:
        # Test 1: Import de la nouvelle classe
        print("\n1️⃣ Test import AnalyseurDiffTopo...")
        from analyseur_diff_topo import AnalyseurDiffTopo, analyser_avec_diff_topo_multiprocessing
        print("✅ Import réussi")
        
        # Test 2: Initialisation
        print("\n2️⃣ Test initialisation...")
        analyseur = AnalyseurDiffTopo()
        print("✅ Initialisation réussie")
        
        # Test 3: Calcul entropie topologique
        print("\n3️⃣ Test calcul entropie topologique...")
        sequence_test = ['0_A_BANKER', '1_B_PLAYER', '0_C_TIE', '1_A_BANKER', '0_B_PLAYER']
        entropie = analyseur.entropie_topologique_index5(sequence_test)
        print(f"✅ Entropie calculée: {entropie:.6f}")
        
        # Test 4: Architecture DIFF_TOPO
        print("\n4️⃣ Test architecture DIFF_TOPO...")
        sequence_complete = [
            '0_A_BANKER', '1_B_PLAYER', '0_C_TIE', '1_A_BANKER', 
            '0_B_PLAYER', '1_C_TIE', '0_A_BANKER', '1_B_PLAYER'
        ]
        resultats = analyseur.architecture_diff_topo(sequence_complete, 5)
        
        print(f"✅ Architecture DIFF_TOPO testée:")
        print(f"   📊 DIFF_TOPO: {resultats['diff_topo']:.6f}")
        print(f"   📊 Ratio L4: {resultats['ratio_l4_topo']:.6f}")
        print(f"   📊 Ratio L5: {resultats['ratio_l5_topo']:.6f}")
        print(f"   📊 Qualité: {resultats['qualite_topo']}")
        
        # Test 5: Génération signatures (échantillon)
        print("\n5️⃣ Test génération signatures...")
        # Note: Test limité car la génération complète prend du temps
        print("⏳ Génération signatures L4 (peut prendre du temps)...")
        signatures_4 = analyseur.generer_signatures_topo_longueur_4()
        print(f"✅ {len(signatures_4):,} signatures L4 générées")
        
        # Test 6: Conditions prédictives (avec données simulées)
        print("\n6️⃣ Test conditions prédictives...")
        donnees_test = [
            {
                'diff_topo': 0.005,
                'ratio_l4_topo': 0.8,
                'ratio_l5_topo': 0.9,
                'pattern_so': 'S'
            },
            {
                'diff_topo': 0.250,
                'ratio_l4_topo': 1.2,
                'ratio_l5_topo': 0.7,
                'pattern_so': 'O'
            }
        ]
        
        conditions_s, conditions_o = analyseur.analyser_conditions_predictives_topo(donnees_test)
        print(f"✅ Conditions analysées: {len(conditions_s)} S, {len(conditions_o)} O")
        
        # Test 7: Compatibilité avec ancienne interface
        print("\n7️⃣ Test compatibilité ancienne interface...")
        from analyse_complete_avec_diff import AnalyseurMetriqueGenerique
        analyseur_ancien = AnalyseurMetriqueGenerique()
        
        # Test méthode obsolète avec délégation
        entropie_ancien = analyseur_ancien.entropie_topologique_index5(sequence_test)
        print(f"✅ Délégation ancienne interface: {entropie_ancien:.6f}")
        
        # Vérifier que les résultats sont identiques
        if abs(entropie - entropie_ancien) < 1e-10:
            print("✅ Résultats identiques entre nouvelle et ancienne interface")
        else:
            print("⚠️ Différence détectée entre interfaces")
        
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        print("✅ Migration DIFF_TOPO validée")
        print("🚀 AnalyseurDiffTopo opérationnel")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERREUR LORS DU TEST: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_comparison():
    """
    Test de comparaison de performance entre ancienne et nouvelle implémentation
    """
    print("\n🏃 TEST DE PERFORMANCE")
    print("=" * 30)
    
    import time
    
    # Séquence de test plus longue
    sequence_longue = []
    patterns = ['0_A_BANKER', '1_B_PLAYER', '0_C_TIE', '1_A_BANKER', '0_B_PLAYER', '1_C_TIE']
    for i in range(50):  # 300 éléments
        sequence_longue.append(patterns[i % len(patterns)])
    
    try:
        # Test nouvelle implémentation
        print("⏱️ Test AnalyseurDiffTopo...")
        from analyseur_diff_topo import AnalyseurDiffTopo
        analyseur_nouveau = AnalyseurDiffTopo()
        
        start_time = time.time()
        entropie_nouveau = analyseur_nouveau.entropie_topologique_index5(sequence_longue)
        temps_nouveau = time.time() - start_time
        
        print(f"✅ Nouvelle implémentation: {entropie_nouveau:.6f} en {temps_nouveau:.3f}s")
        
        # Test ancienne implémentation (délégation)
        print("⏱️ Test délégation ancienne interface...")
        from analyse_complete_avec_diff import AnalyseurMetriqueGenerique
        analyseur_ancien = AnalyseurMetriqueGenerique()
        
        start_time = time.time()
        entropie_ancien = analyseur_ancien.entropie_topologique_index5(sequence_longue)
        temps_ancien = time.time() - start_time
        
        print(f"✅ Délégation ancienne: {entropie_ancien:.6f} en {temps_ancien:.3f}s")
        
        # Comparaison
        if abs(entropie_nouveau - entropie_ancien) < 1e-10:
            print("✅ Résultats identiques")
        else:
            print(f"⚠️ Différence: {abs(entropie_nouveau - entropie_ancien):.10f}")
        
        print(f"📊 Overhead délégation: {temps_ancien - temps_nouveau:.3f}s")
        
    except Exception as e:
        print(f"❌ Erreur test performance: {e}")

if __name__ == "__main__":
    print("🚀 LANCEMENT TESTS MIGRATION DIFF_TOPO")
    print("=" * 60)
    
    # Test principal
    success = test_migration_diff_topo()
    
    if success:
        # Test de performance
        test_performance_comparison()
        
        print("\n🎯 RÉSUMÉ FINAL")
        print("=" * 20)
        print("✅ Migration DIFF_TOPO réussie")
        print("✅ AnalyseurDiffTopo opérationnel")
        print("✅ Compatibilité maintenue")
        print("✅ Performance validée")
        print("\n🚀 Prêt pour utilisation en production !")
    else:
        print("\n❌ ÉCHEC DE LA MIGRATION")
        print("🔧 Vérifier les erreurs ci-dessus")
        sys.exit(1)
