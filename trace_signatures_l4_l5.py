#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TRAÇAGE DES SIGNATURES L4 ET L5
==============================
Trace étape par étape le calcul des signatures topologiques
"""

import math
from analyse_complete_avec_diff import AnalyseurMetriqueGenerique

def tracer_signature_topologique(sequence, nom_sequence):
    """Trace le calcul d'une signature topologique étape par étape"""
    print(f"\n🔍 TRAÇAGE SIGNATURE {nom_sequence}")
    print("=" * 50)
    print(f"Séquence: {sequence}")
    print(f"Longueur: {len(sequence)}")
    
    if not sequence:
        print("❌ Séquence vide → entropie = 0.0")
        return 0.0
    
    n = len(sequence)
    if n <= 1:
        print("❌ Séquence trop courte → entropie = 0.0")
        return 0.0
    
    # ÉTAPE 1: Construction des trajectoires
    print(f"\n📊 ÉTAPE 1: CONSTRUCTION DES TRAJECTOIRES")
    trajectoires = []
    for i in range(n):
        debut = max(0, i-2)  # Fenêtre de 3 éléments max
        fin = min(n, i+2)
        trajectoire = tuple(sequence[debut:fin])
        trajectoires.append(trajectoire)
        print(f"   Position {i}: fenêtre [{debut}:{fin}] → {trajectoire}")
    
    print(f"   Total trajectoires: {len(trajectoires)}")
    
    # ÉTAPE 2: Calcul des ensembles (n,ε)-séparés
    print(f"\n📊 ÉTAPE 2: ENSEMBLES (n,ε)-SÉPARÉS")
    epsilon = 0.3  # 30% de différence minimum
    print(f"   Seuil ε = {epsilon} (30% de différence minimum)")
    
    ensemble_separe = []
    for i, traj in enumerate(trajectoires):
        est_separe = True
        distances = []
        
        for j, traj_existante in enumerate(ensemble_separe):
            # Calculer distance de Hamming normalisée
            if len(traj) != len(traj_existante):
                distance = 1.0
            elif len(traj) == 0:
                distance = 0.0
            else:
                differences = sum(1 for a, b in zip(traj, traj_existante) if a != b)
                distance = differences / len(traj)
            
            distances.append((j, distance))
            
            if distance < epsilon:
                est_separe = False
                print(f"   Traj {i}: {traj} → REJETÉE (distance {distance:.3f} < {epsilon} avec traj {j})")
                break
        
        if est_separe:
            ensemble_separe.append(traj)
            if distances:
                min_dist = min(d[1] for d in distances)
                print(f"   Traj {i}: {traj} → ACCEPTÉE (distance min {min_dist:.3f} ≥ {epsilon})")
            else:
                print(f"   Traj {i}: {traj} → ACCEPTÉE (première trajectoire)")
    
    print(f"   Ensemble séparé final: {len(ensemble_separe)} trajectoires")
    for i, traj in enumerate(ensemble_separe):
        print(f"     {i}: {traj}")
    
    # ÉTAPE 3: Calcul de l'entropie topologique
    print(f"\n📊 ÉTAPE 3: CALCUL ENTROPIE TOPOLOGIQUE")
    s_n_epsilon = len(ensemble_separe)
    print(f"   s_n(ε) = {s_n_epsilon}")
    
    if s_n_epsilon <= 1:
        print("   s_n(ε) ≤ 1 → entropie = 0.0")
        return 0.0
    
    h_top = math.log2(s_n_epsilon) / n
    print(f"   h_top = log₂({s_n_epsilon}) / {n}")
    print(f"   h_top = {math.log2(s_n_epsilon):.6f} / {n}")
    print(f"   h_top = {h_top:.6f}")
    
    return h_top

def analyser_signatures_l4_l5():
    """Analyse les signatures L4 et L5 de l'exemple"""
    print("🔬 ANALYSE DES SIGNATURES L4 ET L5")
    print("=" * 60)
    
    # Séquences de l'exemple
    seq_L4 = ['0_A_PLAYER', '0_B_BANKER', '0_B_PLAYER', '0_A_BANKER']
    seq_L5 = ['0_B_BANKER', '0_A_PLAYER', '0_B_BANKER', '0_B_PLAYER', '0_A_BANKER']
    
    # Calculer avec l'analyseur pour vérification
    analyseur = AnalyseurMetriqueGenerique()
    sig_L4_analyseur = analyseur.entropie_topologique_index5(seq_L4)
    sig_L5_analyseur = analyseur.entropie_topologique_index5(seq_L5)
    
    print(f"🎯 VALEURS ATTENDUES:")
    print(f"   Signature L4: {sig_L4_analyseur:.6f}")
    print(f"   Signature L5: {sig_L5_analyseur:.6f}")
    
    # Tracer L4
    sig_L4_trace = tracer_signature_topologique(seq_L4, "L4")
    
    # Tracer L5
    sig_L5_trace = tracer_signature_topologique(seq_L5, "L5")
    
    # Vérification
    print(f"\n🎯 VÉRIFICATION:")
    print(f"   L4 - Analyseur: {sig_L4_analyseur:.6f}, Tracé: {sig_L4_trace:.6f}, Match: {'✅' if abs(sig_L4_analyseur - sig_L4_trace) < 1e-6 else '❌'}")
    print(f"   L5 - Analyseur: {sig_L5_analyseur:.6f}, Tracé: {sig_L5_trace:.6f}, Match: {'✅' if abs(sig_L5_analyseur - sig_L5_trace) < 1e-6 else '❌'}")
    
    # Test avec d'autres séquences pour voir la variation
    print(f"\n🧪 TEST AVEC D'AUTRES SÉQUENCES:")
    
    sequences_test = [
        (['0_A_BANKER'] * 4, "Uniforme L4"),
        (['1_B_PLAYER', '0_A_BANKER', '1_B_PLAYER', '0_A_BANKER'], "Alternée L4"),
        (['1_B_PLAYER', '0_C_TIE', '1_A_BANKER', '0_B_PLAYER'], "Variée L4"),
        (['0_A_BANKER'] * 5, "Uniforme L5"),
        (['1_B_PLAYER', '0_A_BANKER'] * 2 + ['1_B_PLAYER'], "Alternée L5")
    ]
    
    for seq, desc in sequences_test:
        sig = analyseur.entropie_topologique_index5(seq)
        print(f"   {desc}: {sig:.6f}")
    
    return True

if __name__ == "__main__":
    analyser_signatures_l4_l5()
