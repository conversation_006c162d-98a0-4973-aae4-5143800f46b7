#!/usr/bin/env python3
"""
Script pour analyser les données DIFF_COND et comprendre pourquoi 0 conditions
"""
import json

def analyser_donnees_cond():
    print("🔍 ANALYSE DONNÉES DIFF_COND")
    print("=" * 50)
    
    # Simuler l'extraction d'une petite partie des données DIFF_COND
    # pour voir les scores H et patterns S/O
    
    try:
        # Charger le dataset
        with open('dataset_baccarat_lupasco_20250626_044753.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        parties = data.get('parties', [])
        print(f"📊 {len(parties)} parties chargées")
        
        # Analyser les premières parties pour voir les patterns S/O
        for i in range(min(3, len(parties))):
            partie = parties[i]
            partie_id = partie.get('partie_number', 'inconnu')
            mains = partie.get('mains', [])
            
            print(f"\n🔍 PARTIE {partie_id}: {len(mains)} mains")
            
            # Extraire les INDEX5 et patterns S/O
            index5_values = []
            patterns_so = []
            
            for j, main in enumerate(mains):
                if j >= 4:  # À partir de la main 5
                    index5 = main.get('INDEX5')
                    pattern_so = main.get('pattern_so')
                    
                    index5_values.append(index5)
                    patterns_so.append(pattern_so)
                    
                    if j <= 10:  # Afficher les 6 premières
                        print(f"   Main {j+1}: INDEX5={index5}, Pattern={pattern_so}")
            
            print(f"   📊 INDEX5 extraits: {len(index5_values)}")
            print(f"   📊 Patterns S/O: {patterns_so[:10]}...")
            
            # Compter les patterns S et O
            count_s = patterns_so.count('S')
            count_o = patterns_so.count('O')
            count_autres = len(patterns_so) - count_s - count_o
            
            print(f"   📊 Répartition: {count_s} S, {count_o} O, {count_autres} autres")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    analyser_donnees_cond()
