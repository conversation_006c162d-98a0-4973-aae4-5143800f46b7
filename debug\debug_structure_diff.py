#!/usr/bin/env python3
"""
Script pour analyser la structure des données DIFF
"""
import json

def analyser_structure_diff():
    print("🔍 ANALYSE STRUCTURE DONNÉES DIFF")
    print("=" * 50)

    # Accéder aux données DIFF globales
    try:
        # Importer le module principal
        import analyse_complete_avec_diff

        # Vérifier si les données DIFF globales existent
        if hasattr(analyse_complete_avec_diff, 'donnees_diff_globales'):
            donnees_diff = analyse_complete_avec_diff.donnees_diff_globales
            print(f"📊 {len(donnees_diff)} données DIFF globales trouvées")

            # Analyser la structure des premières données
            for i in range(min(5, len(donnees_diff))):
                donnee = donnees_diff[i]
                print(f"\n🔍 DONNÉE DIFF {i+1}:")
                print(f"   Clés disponibles: {list(donnee.keys())}")

                # Afficher toutes les valeurs
                for cle, valeur in donnee.items():
                    print(f"   {cle}: {valeur}")
        else:
            print("❌ Aucune donnée DIFF globale trouvée")

    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyser_structure_diff()
