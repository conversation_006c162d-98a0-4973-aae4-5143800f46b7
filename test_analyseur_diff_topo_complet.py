"""
TEST COMPLET ANALYSEUR DIFF_TOPO
===============================

Vérification exhaustive de toutes les fonctionnalités
de la classe AnalyseurDiffTopo
"""

import time
import traceback
from analyseur_diff_topo import AnalyseurDiffTopo, analyser_avec_diff_topo_multiprocessing

def test_analyseur_diff_topo_complet():
    """Test exhaustif de AnalyseurDiffTopo"""
    
    print("🔬 VÉRIFICATION COMPLÈTE ANALYSEUR DIFF_TOPO")
    print("=" * 60)
    
    try:
        # ===== TEST 1: INITIALISATION =====
        print("\n1️⃣ Test initialisation...")
        analyseur = AnalyseurDiffTopo()
        print("✅ Initialisation réussie")
        
        # Vérifier les attributs
        assert hasattr(analyseur, 'bases_signatures_4'), "❌ Attribut bases_signatures_4 manquant"
        assert hasattr(analyseur, 'bases_signatures_5'), "❌ Attribut bases_signatures_5 manquant"
        assert hasattr(analyseur, 'seuils_topo'), "❌ Attribut seuils_topo manquant"
        print("✅ Tous les attributs présents")
        
        # ===== TEST 2: ENTROPIE TOPOLOGIQUE =====
        print("\n2️⃣ Test entropie topologique...")
        
        # Séquence test simple
        seq_simple = ["0_A_BANKER", "1_B_PLAYER", "2_A_BANKER"]
        entropie_simple = analyseur.entropie_topologique_index5(seq_simple)
        print(f"✅ Entropie séquence simple: {entropie_simple:.6f}")
        
        # Séquence test complexe
        seq_complexe = [
            "0_A_BANKER", "1_B_PLAYER", "2_A_BANKER", "3_B_PLAYER",
            "4_A_BANKER", "5_B_PLAYER", "6_A_BANKER", "7_B_PLAYER"
        ]
        entropie_complexe = analyseur.entropie_topologique_index5(seq_complexe)
        print(f"✅ Entropie séquence complexe: {entropie_complexe:.6f}")
        
        # Vérifier que l'entropie est dans une plage raisonnable
        assert 0 <= entropie_simple <= 10, f"❌ Entropie simple hors plage: {entropie_simple}"
        assert 0 <= entropie_complexe <= 10, f"❌ Entropie complexe hors plage: {entropie_complexe}"
        
        # ===== TEST 3: DISTANCE HAMMING =====
        print("\n3️⃣ Test distance Hamming...")
        
        traj1 = ["0_A_BANKER", "1_B_PLAYER"]
        traj2 = ["0_A_BANKER", "1_B_PLAYER"]  # Identique
        traj3 = ["2_A_BANKER", "3_B_PLAYER"]  # Différente
        
        dist_identique = analyseur._distance_hamming_normalisee(traj1, traj2)
        dist_differente = analyseur._distance_hamming_normalisee(traj1, traj3)
        
        print(f"✅ Distance trajectoires identiques: {dist_identique:.6f}")
        print(f"✅ Distance trajectoires différentes: {dist_differente:.6f}")
        
        assert dist_identique == 0.0, f"❌ Distance identique devrait être 0: {dist_identique}"
        assert dist_differente > 0.0, f"❌ Distance différente devrait être > 0: {dist_differente}"
        
        # ===== TEST 4: GÉNÉRATION SIGNATURES L4 =====
        print("\n4️⃣ Test génération signatures L4...")
        
        start_time = time.time()
        signatures_l4 = analyseur.generer_signatures_topo_longueur_4()
        temps_l4 = time.time() - start_time
        
        print(f"✅ {len(signatures_l4):,} signatures L4 générées en {temps_l4:.3f}s")
        
        # Vérifier quelques signatures
        assert len(signatures_l4) > 1000, f"❌ Trop peu de signatures L4: {len(signatures_l4)}"
        
        # Vérifier qu'elles sont stockées
        assert 'TOPO' in analyseur.bases_signatures_4, "❌ Signatures L4 non stockées"
        assert len(analyseur.bases_signatures_4['TOPO']) == len(signatures_l4), "❌ Stockage L4 incorrect"
        
        # ===== TEST 5: GÉNÉRATION SIGNATURES L5 =====
        print("\n5️⃣ Test génération signatures L5...")
        
        start_time = time.time()
        signatures_l5 = analyseur.generer_signatures_topo_longueur_5()
        temps_l5 = time.time() - start_time
        
        print(f"✅ {len(signatures_l5):,} signatures L5 générées en {temps_l5:.3f}s")
        
        # Vérifier quelques signatures
        assert len(signatures_l5) > 10000, f"❌ Trop peu de signatures L5: {len(signatures_l5)}"
        
        # Vérifier qu'elles sont stockées
        assert 'TOPO' in analyseur.bases_signatures_5, "❌ Signatures L5 non stockées"
        assert len(analyseur.bases_signatures_5['TOPO']) == len(signatures_l5), "❌ Stockage L5 incorrect"
        
        # ===== TEST 6: CLASSIFICATION QUALITÉ =====
        print("\n6️⃣ Test classification qualité...")
        
        # Tester différentes valeurs DIFF_TOPO
        test_values = [0.005, 0.020, 0.040, 0.080, 0.150, 0.300]
        
        for val in test_values:
            qualite = analyseur._classifier_qualite_topo(val)
            print(f"   DIFF_TOPO {val:.3f} → {qualite}")
            assert isinstance(qualite, str), f"❌ Qualité devrait être string: {type(qualite)}"
            assert qualite.startswith('TOPO_'), f"❌ Qualité devrait commencer par TOPO_: {qualite}"
        
        print("✅ Classification qualité fonctionnelle")
        
        # ===== TEST 7: ARCHITECTURE DIFF_TOPO COMPLÈTE =====
        print("\n7️⃣ Test architecture DIFF_TOPO complète...")
        
        # Séquence test réaliste
        sequence_complete = [
            "0_A_BANKER", "1_B_PLAYER", "2_A_BANKER", "3_B_PLAYER",
            "4_A_BANKER", "5_B_PLAYER", "6_A_BANKER", "7_B_PLAYER",
            "8_A_BANKER", "9_B_PLAYER"
        ]
        
        position_main = 6  # Position valide (≥ 5)
        
        start_time = time.time()
        resultats = analyseur.architecture_diff_topo(sequence_complete, position_main)
        temps_archi = time.time() - start_time
        
        print(f"✅ Architecture DIFF_TOPO exécutée en {temps_archi:.3f}s")
        
        # Vérifier la structure des résultats
        champs_requis = [
            'ratio_l4_topo', 'ratio_l5_topo', 'signature_l4_topo',
            'signature_l5_topo', 'signature_globale_topo', 'qualite_topo', 'diff_topo'
        ]
        
        for champ in champs_requis:
            assert champ in resultats, f"❌ Champ manquant: {champ}"
            assert isinstance(resultats[champ], (int, float, str)), f"❌ Type incorrect pour {champ}"
        
        print(f"   📊 DIFF_TOPO: {resultats['diff_topo']:.6f}")
        print(f"   📊 Ratio L4: {resultats['ratio_l4_topo']:.6f}")
        print(f"   📊 Ratio L5: {resultats['ratio_l5_topo']:.6f}")
        print(f"   📊 Qualité: {resultats['qualite_topo']}")
        
        # ===== TEST 8: CONDITIONS PRÉDICTIVES =====
        print("\n8️⃣ Test conditions prédictives...")
        
        # Créer des données test
        donnees_test = []
        for i in range(10):
            donnees_test.append({
                'diff_topo': 0.05 + i * 0.02,
                'pattern_so': 'S' if i % 2 == 0 else 'O',
                'ratio_l4_topo': 0.1 + i * 0.01,
                'ratio_l5_topo': 0.12 + i * 0.01
            })
        
        conditions_s, conditions_o = analyseur.analyser_conditions_predictives_topo(donnees_test)
        
        print(f"✅ Conditions S: {len(conditions_s)}")
        print(f"✅ Conditions O: {len(conditions_o)}")
        
        # ===== TEST 9: FONCTION MULTIPROCESSING =====
        print("\n9️⃣ Test fonction multiprocessing...")
        
        # Test avec données minimales
        parties_test = [
            {
                'partie_number': 1,
                'mains': [
                    {'index5_combined': '0_A_BANKER'},
                    {'index5_combined': '1_B_PLAYER'},
                    {'index5_combined': '2_A_BANKER'},
                    {'index5_combined': '3_B_PLAYER'},
                    {'index5_combined': '4_A_BANKER'},
                    {'index5_combined': '5_B_PLAYER'}
                ]
            }
        ]
        
        donnees_diff_test = [
            {
                'partie_id': 1,
                'main': 5,
                'pattern': 'S',
                'diff_l4': 0.05,
                'diff_l5': 0.06,
                'diff': 0.01
            }
        ]
        
        try:
            resultats_multi = analyser_avec_diff_topo_multiprocessing(parties_test, donnees_diff_test)
            print(f"✅ Multiprocessing: {len(resultats_multi)} résultats")
        except Exception as e:
            print(f"⚠️ Multiprocessing: {str(e)} (normal si pas de données suffisantes)")
        
        # ===== RÉSUMÉ FINAL =====
        print("\n" + "=" * 60)
        print("🎉 VÉRIFICATION COMPLÈTE RÉUSSIE !")
        print("=" * 60)
        print("✅ Initialisation")
        print("✅ Entropie topologique")
        print("✅ Distance Hamming")
        print("✅ Signatures L4")
        print("✅ Signatures L5")
        print("✅ Classification qualité")
        print("✅ Architecture DIFF_TOPO")
        print("✅ Conditions prédictives")
        print("✅ Interface multiprocessing")
        print("\n🚀 AnalyseurDiffTopo ENTIÈREMENT OPÉRATIONNEL !")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERREUR LORS DE LA VÉRIFICATION: {str(e)}")
        print("🔧 Traceback complet:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_analyseur_diff_topo_complet()
