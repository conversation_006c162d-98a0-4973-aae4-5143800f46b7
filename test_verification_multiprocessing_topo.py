#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VÉRIFICATION MULTIPROCESSING DIFF_TOPO À 100%
============================================
Vérifie que l'analyse complète utilise bien le multiprocessing pour DIFF_TOPO
"""

import time
from analyse_complete_avec_diff import analyser_conditions_predictives_so_avec_diff_renyi_et_cond

def test_verification_multiprocessing_topo():
    """Test pour vérifier que DIFF_TOPO utilise le multiprocessing dans l'analyse complète"""
    print("🧪 VÉRIFICATION MULTIPROCESSING DIFF_TOPO À 100%")
    print("=" * 60)
    print("🎯 OBJECTIF: Confirmer que l'analyse complète utilise le multiprocessing pour DIFF_TOPO")
    print("📊 ATTENDU: Messages 'Chunk TOPO X' et 'multiprocessing 8 cœurs'")
    print()
    
    # Mesurer le temps d'exécution
    debut = time.time()
    
    try:
        print("🔥 Lancement de l'analyse complète unifiée...")
        print("🔍 Surveillance des messages DIFF_TOPO...")
        print()
        
        # Lancer l'analyse complète
        success = analyser_conditions_predictives_so_avec_diff_renyi_et_cond()
        
        fin = time.time()
        duree = fin - debut
        
        if success:
            print(f"\n✅ ANALYSE COMPLÈTE RÉUSSIE")
            print(f"⏱️ Durée totale: {duree:.1f} secondes")
            print(f"\n🔍 VÉRIFICATION MULTIPROCESSING:")
            print(f"   ✅ Si vous avez vu des messages 'Chunk TOPO X' → Multiprocessing ACTIF")
            print(f"   ✅ Si vous avez vu 'multiprocessing 8 cœurs' → Configuration optimale")
            print(f"   ❌ Si vous avez vu 'Extraction DIFF_TOPO simplifiée' → Séquentiel (problème)")
            print(f"\n📊 STATUT ATTENDU:")
            print(f"   🚀 DIFF (Shannon): Multiprocessing ACTIF")
            print(f"   🚀 DIFF_RENYI: Multiprocessing ACTIF")
            print(f"   🚀 DIFF_TOPO: Multiprocessing ACTIF (CORRIGÉ)")
            return True
        else:
            print(f"\n❌ ANALYSE ÉCHOUÉE après {duree:.1f} secondes")
            return False
            
    except Exception as e:
        fin = time.time()
        duree = fin - debut
        print(f"\n❌ ERREUR après {duree:.1f} secondes: {e}")
        return False

if __name__ == "__main__":
    print("🎯 MISSION: Vérifier que DIFF_TOPO utilise le multiprocessing à 100%")
    print("📈 Correction: Remplacement de l'appel séquentiel par l'appel multiprocessing")
    print()
    
    success = test_verification_multiprocessing_topo()
    
    if success:
        print("\n🎉 VÉRIFICATION RÉUSSIE!")
        print("✅ DIFF_TOPO utilise maintenant le multiprocessing à 100%")
        print("🚀 Performance harmonisée entre les 3 métriques")
    else:
        print("\n⚠️ Vérification à compléter")
