#!/usr/bin/env python3
"""
Test simple pour diagnostiquer l'entropie topologique
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analyse_complete_avec_diff import AnalyseurMetriqueGenerique

def test_entropie_topologique():
    """Test des calculs d'entropie topologique avec différentes séquences"""
    
    print("🔬 TEST ENTROPIE TOPOLOGIQUE")
    print("=" * 50)
    
    analyseur = AnalyseurMetriqueGenerique()
    
    # Test avec différentes séquences INDEX5
    sequences_test = [
        ['1_A_BANKER', '0_B_PLAYER', '1_C_TIE', '0_A_BANKER'],  # L4
        ['1_A_BANKER', '0_B_PLAYER', '1_C_TIE', '0_A_BANKER', '1_B_PLAYER'],  # L5
        ['1_A_BANKER', '1_A_BANKER', '1_A_BANKER', '1_A_BANKER'],  # Répétitive
        ['0_C_PLAYER', '1_C_BANKER', '0_C_TIE', '1_C_PLAYER'],  # Alternance C
        ['1_B_TIE', '1_B_TIE', '1_B_TIE', '1_B_TIE', '1_B_TIE'],  # Constante
    ]
    
    print("\n📊 Test avec différentes séquences:")
    for i, seq in enumerate(sequences_test):
        entropie = analyseur.entropie_topologique_index5(seq)
        print(f"Séquence {i+1}: {seq}")
        print(f"  → Entropie topologique: {entropie:.6f}")
        print()
    
    # Test spécifique pour comprendre le problème des ratios
    print("\n🔍 TEST RATIOS PROBLÉMATIQUES:")
    
    # Simuler une séquence plus longue
    seq_globale = [
        '1_A_BANKER', '0_B_PLAYER', '1_C_TIE', '0_A_BANKER', '1_B_PLAYER',
        '0_C_BANKER', '1_A_TIE', '0_B_BANKER', '1_C_PLAYER', '0_A_TIE',
        '1_B_BANKER', '0_C_PLAYER', '1_A_BANKER', '0_B_TIE', '1_C_BANKER'
    ]
    
    # Extraire L4 et L5 comme dans l'architecture RÉELLE
    position_main = 10  # Main 11 (index 10) - séquence plus longue
    seq_L4 = seq_globale[position_main-3:position_main+1]  # [7:11] - 4 éléments
    seq_L5 = seq_globale[position_main-4:position_main+1]  # [6:11] - 5 éléments
    seq_reference = seq_globale[1:position_main+1]  # [1:11] - 10 éléments (DIFFÉRENT de L4!)
    
    print(f"Séquence globale ({len(seq_globale)} éléments): {seq_globale}")
    print(f"seq_L4 (position {position_main-3}:{position_main+1}, {len(seq_L4)} éléments): {seq_L4}")
    print(f"seq_L5 (position {position_main-4}:{position_main+1}, {len(seq_L5)} éléments): {seq_L5}")
    print(f"seq_reference (position 1:{position_main+1}, {len(seq_reference)} éléments): {seq_reference}")
    print()
    
    # Calculer les entropies
    entropie_L4 = analyseur.entropie_topologique_index5(seq_L4)
    entropie_L5 = analyseur.entropie_topologique_index5(seq_L5)
    entropie_globale = analyseur.entropie_topologique_index5(seq_reference)
    
    print(f"Entropie L4: {entropie_L4:.6f}")
    print(f"Entropie L5: {entropie_L5:.6f}")
    print(f"Entropie globale: {entropie_globale:.6f}")
    print()
    
    # Calculer les ratios
    ratio_L4 = entropie_L4 / entropie_globale if entropie_globale > 0 else 0
    ratio_L5 = entropie_L5 / entropie_globale if entropie_globale > 0 else 0
    diff_topo = abs(ratio_L4 - ratio_L5)
    
    print(f"Ratio L4: {ratio_L4:.6f}")
    print(f"Ratio L5: {ratio_L5:.6f}")
    print(f"DIFF_TOPO: {diff_topo:.6f}")
    
    # Diagnostic du problème
    print("\n🔍 DIAGNOSTIC:")
    if entropie_globale == 0:
        print("❌ PROBLÈME: Entropie globale = 0")
    elif entropie_L4 == entropie_L5:
        print("❌ PROBLÈME: Entropies L4 et L5 identiques")
    elif ratio_L4 == 0.5:
        print("❌ PROBLÈME: Ratio L4 = 0.5 (suspect)")
    else:
        print("✅ Calculs semblent corrects")

if __name__ == "__main__":
    test_entropie_topologique()
