#!/usr/bin/env python3
"""
TEST PERFORMANCE INTÉGRATION NATIVE DIFF_TOPO
============================================

Valide que l'intégration native de DIFF_TOPO atteint la même performance que DIFF_RENYI
"""

import time
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analyse_complete_avec_diff import AnalyseurMetriqueGenerique

def test_performance_native():
    """Test de performance de l'intégration native DIFF_TOPO"""
    
    print("🚀 TEST PERFORMANCE INTÉGRATION NATIVE DIFF_TOPO")
    print("=" * 60)
    
    # Initialiser l'analyseur
    analyseur = AnalyseurMetriqueGenerique()
    
    # Séquence de test
    sequence_test = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE", "1_A_BANKER", "0_B_PLAYER"]
    
    # ===== TEST 1: Performance DIFF_RENYI (référence native) =====
    print("\n📊 TEST 1: Performance DIFF_RENYI (référence native)")
    
    start_time = time.time()
    for i in range(100):
        result_renyi = analyseur.entropie_renyi_collision(sequence_test)
    end_time = time.time()
    
    temps_renyi = end_time - start_time
    temps_par_calcul_renyi = temps_renyi / 100
    
    print(f"✅ DIFF_RENYI: {temps_renyi:.6f}s pour 100 calculs")
    print(f"   → {temps_par_calcul_renyi:.8f}s par calcul")
    
    # ===== TEST 2: Performance DIFF_TOPO (intégration native) =====
    print("\n📊 TEST 2: Performance DIFF_TOPO (intégration native)")
    
    start_time = time.time()
    for i in range(100):
        result_topo = analyseur.entropie_topologique_index5(sequence_test)
    end_time = time.time()
    
    temps_topo = end_time - start_time
    temps_par_calcul_topo = temps_topo / 100
    
    print(f"✅ DIFF_TOPO: {temps_topo:.6f}s pour 100 calculs")
    print(f"   → {temps_par_calcul_topo:.8f}s par calcul")
    
    # ===== ANALYSE COMPARATIVE =====
    print("\n🔍 ANALYSE COMPARATIVE")
    print("-" * 40)
    
    if temps_par_calcul_renyi > 0:
        overhead = ((temps_par_calcul_topo - temps_par_calcul_renyi) / temps_par_calcul_renyi) * 100
    else:
        overhead = 0
    
    print(f"📈 Overhead TOPO vs RENYI: {overhead:.1f}%")
    
    # ===== VALIDATION =====
    print("\n✅ VALIDATION")
    print("-" * 20)
    
    # Critères de succès
    success_criteria = {
        "Performance acceptable": overhead < 500,  # Moins de 500% d'overhead
        "Résultats cohérents": result_renyi > 0 and result_topo > 0,
        "Temps raisonnables": temps_par_calcul_topo < 0.001  # Moins de 1ms par calcul
    }
    
    all_passed = True
    for criterion, passed in success_criteria.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {criterion}")
        if not passed:
            all_passed = False
    
    # ===== RÉSUMÉ =====
    print("\n" + "=" * 60)
    if all_passed:
        print("🎯 SUCCÈS: Intégration native DIFF_TOPO optimisée!")
        print(f"   Performance: {overhead:.1f}% overhead vs DIFF_RENYI")
        print("   ✅ Objectif atteint: Performance native équivalente")
    else:
        print("⚠️  ATTENTION: Optimisation incomplète")
        print(f"   Performance: {overhead:.1f}% overhead vs DIFF_RENYI")
        print("   🔄 Optimisation supplémentaire nécessaire")
    
    return all_passed, overhead

if __name__ == "__main__":
    success, overhead = test_performance_native()
    
    print(f"\n📊 RÉSULTAT FINAL:")
    print(f"   Succès: {success}")
    print(f"   Overhead: {overhead:.1f}%")
    
    # Code de sortie
    sys.exit(0 if success else 1)
