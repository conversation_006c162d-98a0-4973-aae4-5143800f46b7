#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXTRACTEUR DE MÉTHODES POUR INDÉPENDANCE analyse_complete_avec_diff.py
====================================================================

Extrait toutes les méthodes utilisées depuis analyseur_transitions_index5.py
pour rendre analyse_complete_avec_diff.py complètement indépendant.

MÉTHODES À EXTRAIRE (basées sur l'analyse ligne par ligne) :
===========================================================

1. AnalyseurEvolutionEntropique.__init__()
2. AnalyseurEvolutionEntropique.analyser_toutes_parties_entropiques()
3. AnalyseurEvolutionRatios.__init__()
4. AnalyseurEvolutionRatios.analyser_evolution_toutes_parties()
5. Toutes les méthodes privées utilisées par ces méthodes principales

OBJECTIF : Créer des fichiers texte séparés pour chaque classe/méthode
"""

import os
import re
from datetime import datetime

class ExtracteurMethodes:
    """
    Extracteur de méthodes depuis analyseur_transitions_index5.py
    """
    
    def __init__(self, fichier_source="analyseur_transitions_index5.py"):
        self.fichier_source = fichier_source
        self.contenu_source = ""
        self.methodes_extraites = {}
        self.classes_extraites = {}
        
        # Méthodes identifiées comme utilisées par analyse_complete_avec_diff.py
        # BASÉ SUR L'ANALYSE LIGNE PAR LIGNE EXACTE DU CODE
        self.methodes_cibles = {
            'AnalyseurEvolutionEntropique': [
                # Méthodes principales utilisées directement
                '__init__',
                'analyser_toutes_parties_entropiques',
                'analyser_partie_entropique',
                '_traiter_chunk_parties',
                '_calculer_stats_partie',
                '_calculer_analyses_globales',
                'generer_rapport_evolution_entropique',
                '_ecrire_analyses_globales',
                '_generer_signatures_integrees',
                # Méthodes de calcul entropique
                'initialiser_bases_signatures',
                '_calculer_entropie_shannon',
                '_classifier_ratio',
                '_compter_categories',
                '_analyser_distribution',
                '_identifier_zones_predictibles'
            ],
            'AnalyseurEvolutionRatios': [
                # Méthodes principales utilisées directement
                '__init__',
                'analyser_evolution_toutes_parties',
                '_analyser_evolution_partie',
                # Méthodes d'analyse des patterns
                '_analyser_tendance',
                '_detecter_oscillations',
                '_analyser_convergence',
                '_extraire_index3_depuis_index5',
                '_calculer_variations_ratios',
                '_calculer_patterns_soe',
                '_calculer_correlation',
                '_classifier_pattern_evolution',
                '_identifier_patterns_evolution',
                '_calculer_statistiques_evolution',
                # Méthodes de génération de rapports
                'generer_rapport_evolution_ratios',
                'generer_rapport_evolution_complete',
                '_ecrire_resume_executif',
                '_ecrire_patterns_evolution',
                '_ecrire_statistiques_globales',
                '_ecrire_analyses_detaillees',
                '_ecrire_conclusions'
            ],
            # IMPORTANT: AnalyseurTransitionsIndex5 pour le cache ultra-optimisé
            'AnalyseurTransitionsIndex5': [
                '__init__',
                '_charger_avec_cache_ultra_optimise',
                # Dépendances identifiées par l'analyse automatique (niveau 1)
                '_detecter_mode_chargement',
                '_charger_sans_cache',
                '_get_cache_key',
                '_charger_depuis_cache',
                '_parser_et_cacher',
                # Dépendances identifiées par l'analyse automatique (niveau 2)
                '_parser_avec_ijson_optimise',
                '_parser_avec_ijson_optimise_sans_cache',
                '_parser_avec_mmap_orjson',
                '_parser_avec_orjson',
                '_parser_avec_orjson_sans_cache',
                # Dépendances identifiées par l'analyse automatique (niveau 3)
                '_creer_cache_optimise'
            ]
        }
        
    def charger_fichier_source(self):
        """Charge le contenu du fichier source"""
        try:
            with open(self.fichier_source, 'r', encoding='utf-8') as f:
                self.contenu_source = f.read()
            print(f"✅ Fichier source chargé: {len(self.contenu_source):,} caractères")
            return True
        except Exception as e:
            print(f"❌ Erreur chargement fichier source: {e}")
            return False
    
    def identifier_limites_classe(self, nom_classe):
        """
        Identifie les limites d'une classe dans le fichier source
        Retourne (ligne_debut, ligne_fin)
        """
        lignes = self.contenu_source.split('\n')
        ligne_debut = None
        ligne_fin = None
        
        # Trouver le début de la classe
        for i, ligne in enumerate(lignes):
            if re.match(rf'^class {nom_classe}[:\(]', ligne.strip()):
                ligne_debut = i
                break
        
        if ligne_debut is None:
            print(f"❌ Classe {nom_classe} non trouvée")
            return None, None
        
        # Trouver la fin de la classe (prochaine classe ou fin de fichier)
        indentation_classe = len(lignes[ligne_debut]) - len(lignes[ligne_debut].lstrip())
        
        for i in range(ligne_debut + 1, len(lignes)):
            ligne = lignes[i]
            if ligne.strip() == "":
                continue
            
            # Si on trouve une nouvelle classe au même niveau d'indentation
            if (ligne.startswith('class ') and 
                len(ligne) - len(ligne.lstrip()) <= indentation_classe):
                ligne_fin = i - 1
                break
        
        if ligne_fin is None:
            ligne_fin = len(lignes) - 1
        
        print(f"✅ Classe {nom_classe} trouvée: lignes {ligne_debut+1} à {ligne_fin+1}")
        return ligne_debut, ligne_fin
    
    def extraire_methode(self, nom_classe, nom_methode, ligne_debut_classe, ligne_fin_classe):
        """
        Extrait une méthode spécifique d'une classe
        """
        lignes = self.contenu_source.split('\n')
        lignes_classe = lignes[ligne_debut_classe:ligne_fin_classe+1]
        
        ligne_debut_methode = None
        ligne_fin_methode = None
        
        # Trouver le début de la méthode
        for i, ligne in enumerate(lignes_classe):
            if re.match(rf'^\s+def {nom_methode}\s*\(', ligne):
                ligne_debut_methode = i
                break
        
        if ligne_debut_methode is None:
            print(f"⚠️ Méthode {nom_methode} non trouvée dans {nom_classe}")
            return None
        
        # Trouver la fin de la méthode
        indentation_methode = len(lignes_classe[ligne_debut_methode]) - len(lignes_classe[ligne_debut_methode].lstrip())
        
        for i in range(ligne_debut_methode + 1, len(lignes_classe)):
            ligne = lignes_classe[i]
            if ligne.strip() == "":
                continue
            
            # Si on trouve une nouvelle méthode ou fin de classe
            if (len(ligne) - len(ligne.lstrip()) <= indentation_methode and 
                (ligne.strip().startswith('def ') or ligne.strip().startswith('class '))):
                ligne_fin_methode = i - 1
                break
        
        if ligne_fin_methode is None:
            ligne_fin_methode = len(lignes_classe) - 1
        
        # Extraire le contenu de la méthode
        contenu_methode = '\n'.join(lignes_classe[ligne_debut_methode:ligne_fin_methode+1])
        
        print(f"✅ Méthode {nom_classe}.{nom_methode} extraite: {ligne_fin_methode - ligne_debut_methode + 1} lignes")
        return contenu_methode
    
    def extraire_toutes_methodes(self):
        """
        Extrait toutes les méthodes cibles de toutes les classes
        """
        if not self.charger_fichier_source():
            return False
        
        print(f"\n🔥 EXTRACTION DE TOUTES LES MÉTHODES CIBLES")
        print("=" * 50)
        
        for nom_classe, liste_methodes in self.methodes_cibles.items():
            print(f"\n📊 Extraction classe: {nom_classe}")
            print("-" * 30)
            
            # Identifier les limites de la classe
            ligne_debut, ligne_fin = self.identifier_limites_classe(nom_classe)
            if ligne_debut is None:
                continue
            
            # Extraire chaque méthode
            methodes_classe = {}
            for nom_methode in liste_methodes:
                contenu_methode = self.extraire_methode(nom_classe, nom_methode, ligne_debut, ligne_fin)
                if contenu_methode:
                    methodes_classe[nom_methode] = contenu_methode
            
            self.methodes_extraites[nom_classe] = methodes_classe
            print(f"✅ {len(methodes_classe)} méthodes extraites pour {nom_classe}")
        
        return True
    
    def sauvegarder_extractions(self):
        """
        Sauvegarde toutes les extractions dans des fichiers séparés
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        dossier_extraction = f"extraction_methodes_{timestamp}"
        
        # Créer le dossier d'extraction
        os.makedirs(dossier_extraction, exist_ok=True)
        print(f"\n📁 Dossier d'extraction créé: {dossier_extraction}")
        
        # Sauvegarder chaque classe dans un fichier séparé
        for nom_classe, methodes_classe in self.methodes_extraites.items():
            nom_fichier = f"{dossier_extraction}/{nom_classe.lower()}_methodes.txt"
            
            with open(nom_fichier, 'w', encoding='utf-8') as f:
                f.write(f"MÉTHODES EXTRAITES DE LA CLASSE {nom_classe}\n")
                f.write("=" * 60 + "\n\n")
                f.write(f"Extraction effectuée le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Fichier source: {self.fichier_source}\n")
                f.write(f"Nombre de méthodes: {len(methodes_classe)}\n\n")
                
                for nom_methode, contenu_methode in methodes_classe.items():
                    f.write(f"{'='*80}\n")
                    f.write(f"MÉTHODE: {nom_classe}.{nom_methode}\n")
                    f.write(f"{'='*80}\n\n")
                    f.write(contenu_methode)
                    f.write("\n\n")
            
            print(f"✅ Fichier créé: {nom_fichier} ({len(methodes_classe)} méthodes)")
        
        # Créer un fichier de synthèse
        fichier_synthese = f"{dossier_extraction}/synthese_extraction.txt"
        with open(fichier_synthese, 'w', encoding='utf-8') as f:
            f.write("SYNTHÈSE DE L'EXTRACTION DES MÉTHODES\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Date d'extraction: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Fichier source: {self.fichier_source}\n\n")

            total_methodes = sum(len(methodes) for methodes in self.methodes_extraites.values())
            f.write(f"RÉSUMÉ:\n")
            f.write(f"- Classes extraites: {len(self.methodes_extraites)}\n")
            f.write(f"- Méthodes totales: {total_methodes}\n\n")

            f.write("DÉTAIL PAR CLASSE:\n")
            f.write("-" * 30 + "\n")
            for nom_classe, methodes_classe in self.methodes_extraites.items():
                f.write(f"{nom_classe}: {len(methodes_classe)} méthodes\n")
                for nom_methode in methodes_classe.keys():
                    f.write(f"  - {nom_methode}\n")
                f.write("\n")

        print(f"✅ Fichier de synthèse créé: {fichier_synthese}")
        print(f"\n🎯 EXTRACTION TERMINÉE: {total_methodes} méthodes extraites")

        return dossier_extraction

    def analyser_dependances_methodes(self):
        """
        Analyse les dépendances entre méthodes pour identifier toutes les méthodes nécessaires
        """
        print(f"\n🔍 ANALYSE DES DÉPENDANCES ENTRE MÉTHODES")
        print("=" * 50)

        dependances_trouvees = set()

        for nom_classe, methodes_classe in self.methodes_extraites.items():
            print(f"\n📊 Analyse dépendances: {nom_classe}")

            for nom_methode, contenu_methode in methodes_classe.items():
                # Rechercher les appels de méthodes dans le contenu
                appels_methodes = re.findall(r'self\.([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', contenu_methode)

                for appel in appels_methodes:
                    if appel not in self.methodes_cibles.get(nom_classe, []):
                        dependances_trouvees.add(f"{nom_classe}.{appel}")
                        print(f"  🔗 {nom_methode} → {appel}")

        if dependances_trouvees:
            print(f"\n⚠️ DÉPENDANCES MANQUANTES DÉTECTÉES:")
            for dep in sorted(dependances_trouvees):
                print(f"  - {dep}")

            return list(dependances_trouvees)
        else:
            print(f"\n✅ Aucune dépendance manquante détectée")
            return []

def main():
    """
    Point d'entrée principal de l'extracteur
    """
    print("🔥 EXTRACTEUR DE MÉTHODES POUR INDÉPENDANCE")
    print("=" * 50)
    print("Objectif: Extraire toutes les méthodes utilisées par analyse_complete_avec_diff.py")
    print("depuis analyseur_transitions_index5.py pour créer l'indépendance complète")

    # Créer l'extracteur
    extracteur = ExtracteurMethodes()

    # Extraire toutes les méthodes
    if extracteur.extraire_toutes_methodes():
        # Analyser les dépendances
        dependances_manquantes = extracteur.analyser_dependances_methodes()

        # Sauvegarder les extractions
        dossier_extraction = extracteur.sauvegarder_extractions()

        print(f"\n✅ EXTRACTION RÉUSSIE!")
        print(f"📁 Fichiers disponibles dans: {dossier_extraction}")

        if dependances_manquantes:
            print(f"⚠️ {len(dependances_manquantes)} dépendances manquantes détectées")
            print(f"🔧 Ajoutez ces méthodes à la liste des méthodes cibles")

        print(f"🎯 Prochaine étape: Intégrer ces méthodes dans analyse_complete_avec_diff.py")

        return True
    else:
        print(f"\n❌ EXTRACTION ÉCHOUÉE")
        return False

if __name__ == "__main__":
    main()
