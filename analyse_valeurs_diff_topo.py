#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSE DES VALEURS DIFF_TOPO
============================
Analyse les valeurs attribuées aux séquences L4, L5 et DIFF_TOPO
"""

import json
from analyse_complete_avec_diff import AnalyseurMetriqueGenerique, detecter_dataset_le_plus_recent

def analyser_valeurs_diff_topo():
    """Analyse détaillée des valeurs DIFF_TOPO"""
    print("🔬 ANALYSE DES VALEURS DIFF_TOPO")
    print("=" * 50)
    
    # Initialiser l'analyseur
    analyseur = AnalyseurMetriqueGenerique()
    
    # 1. ANALYSER LES SIGNATURES L4
    print("\n📊 1. ANALYSE SIGNATURES LONGUEUR 4")
    print("-" * 40)
    
    signatures_l4 = analyseur.generer_signatures_topo_longueur_4()
    print(f"✅ {len(signatures_l4)} signatures L4 générées")
    
    # Analyser quelques exemples
    exemples_l4 = list(signatures_l4.items())[:10]
    print("\n🔍 EXEMPLES SIGNATURES L4:")
    for sequence, signature in exemples_l4:
        print(f"   {sequence} → {signature:.6f}")
    
    # Statistiques L4
    valeurs_l4 = list(signatures_l4.values())
    print(f"\n📈 STATISTIQUES L4:")
    print(f"   Min: {min(valeurs_l4):.6f}")
    print(f"   Max: {max(valeurs_l4):.6f}")
    print(f"   Moyenne: {sum(valeurs_l4)/len(valeurs_l4):.6f}")
    print(f"   Étendue: {max(valeurs_l4) - min(valeurs_l4):.6f}")
    
    # 2. ANALYSER LES SIGNATURES L5
    print("\n📊 2. ANALYSE SIGNATURES LONGUEUR 5")
    print("-" * 40)
    
    signatures_l5 = analyseur.generer_signatures_topo_longueur_5()
    print(f"✅ {len(signatures_l5)} signatures L5 générées")
    
    # Analyser quelques exemples
    exemples_l5 = list(signatures_l5.items())[:10]
    print("\n🔍 EXEMPLES SIGNATURES L5:")
    for sequence, signature in exemples_l5:
        print(f"   {sequence} → {signature:.6f}")
    
    # Statistiques L5
    valeurs_l5 = list(signatures_l5.values())
    print(f"\n📈 STATISTIQUES L5:")
    print(f"   Min: {min(valeurs_l5):.6f}")
    print(f"   Max: {max(valeurs_l5):.6f}")
    print(f"   Moyenne: {sum(valeurs_l5)/len(valeurs_l5):.6f}")
    print(f"   Étendue: {max(valeurs_l5) - min(valeurs_l5):.6f}")
    
    # 3. ANALYSER QUELQUES CALCULS DIFF_TOPO RÉELS
    print("\n📊 3. ANALYSE CALCULS DIFF_TOPO RÉELS")
    print("-" * 40)
    
    # Charger le dataset
    dataset_path, _ = detecter_dataset_le_plus_recent()
    with open(dataset_path, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    parties = dataset.get('parties', [])
    print(f"📄 Dataset chargé: {len(parties)} parties")
    
    # Analyser les premières parties
    exemples_diff_topo = []
    for i, partie in enumerate(parties[:5]):  # 5 premières parties
        partie_id = partie.get('partie_number', i+1)
        mains = partie.get('mains', [])
        
        # Construire la séquence INDEX5
        sequence_index5 = []
        for main in mains:
            index5_combined = main.get('index5_combined', '')
            if index5_combined:
                sequence_index5.append(index5_combined)
        
        print(f"\n🎯 PARTIE {partie_id}:")
        print(f"   Mains: {len(sequence_index5)}")
        
        if len(sequence_index5) >= 5:
            # Analyser quelques positions
            for position in range(4, min(9, len(sequence_index5))):  # Positions 5-9
                try:
                    resultats = analyseur.architecture_diff_topo(sequence_index5, position)
                    
                    seq_l4 = sequence_index5[position-3:position+1]
                    seq_l5 = sequence_index5[position-4:position+1]
                    
                    print(f"   Main {position+1}:")
                    print(f"     L4: {seq_l4}")
                    print(f"     L5: {seq_l5}")
                    # Récupérer les valeurs avec les bonnes clés et valeurs par défaut numériques
                    sig_l4 = resultats.get('signature_l4_topo', 0.0)
                    sig_l5 = resultats.get('signature_l5_topo', 0.0)
                    ratio_l4 = resultats.get('ratio_l4_topo', 0.0)
                    ratio_l5 = resultats.get('ratio_l5_topo', 0.0)
                    diff_topo = resultats.get('diff_topo', 0.0)  # Clé correcte : 'diff_topo' (minuscule)

                    print(f"     Signature L4: {sig_l4:.6f}")
                    print(f"     Signature L5: {sig_l5:.6f}")
                    print(f"     Ratio L4: {ratio_l4:.6f}")
                    print(f"     Ratio L5: {ratio_l5:.6f}")
                    print(f"     DIFF_TOPO: {diff_topo:.6f}")

                    exemples_diff_topo.append(diff_topo)
                    
                except Exception as e:
                    print(f"     Erreur position {position+1}: {e}")
                    continue
        
        if i >= 2:  # Limiter à 3 parties pour l'exemple
            break
    
    # 4. STATISTIQUES DIFF_TOPO
    if exemples_diff_topo:
        print(f"\n📈 STATISTIQUES DIFF_TOPO ({len(exemples_diff_topo)} exemples):")
        print(f"   Min: {min(exemples_diff_topo):.6f}")
        print(f"   Max: {max(exemples_diff_topo):.6f}")
        print(f"   Moyenne: {sum(exemples_diff_topo)/len(exemples_diff_topo):.6f}")
        print(f"   Étendue: {max(exemples_diff_topo) - min(exemples_diff_topo):.6f}")
    
    # 5. COMPARAISON AVEC FORMULE THÉORIQUE
    print(f"\n🧮 VÉRIFICATION FORMULE TOPOLOGIQUE:")
    print(f"   Formule: h_top(T) = lim_n→∞ (1/n) log s_n(ε,T)")
    print(f"   Implémentation: entropie_topologique_index5()")
    print(f"   Base logarithme: log₂ (pour cohérence avec Shannon)")
    
    return True

if __name__ == "__main__":
    analyser_valeurs_diff_topo()
