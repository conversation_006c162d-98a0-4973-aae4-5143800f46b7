#!/usr/bin/env python3
"""
TEST PERFORMANCE FINALE PURE - DIFF_TOPO vs DIFF_RENYI
======================================================

Validation finale que l'élimination des rechargements par chunk
a permis d'atteindre une performance pure identique à DIFF_RENYI
"""

import time
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analyse_complete_avec_diff import AnalyseurMetriqueGenerique

def test_performance_finale():
    """Test de performance finale après élimination des rechargements"""
    
    print("🚀 TEST PERFORMANCE FINALE PURE - ÉLIMINATION RECHARGEMENTS")
    print("=" * 70)
    
    # Initialiser l'analyseur
    analyseur = AnalyseurMetriqueGenerique()
    
    # Séquences de test variées
    sequences_test = [
        ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE", "1_A_BANKER", "0_B_PLAYER"],
        ["1_C_PLAYER", "0_A_TIE", "0_B_BANKER", "0_C_PLAYER", "1_A_TIE"],
        ["0_B_TIE", "0_A_BANKER", "0_C_PLAYER", "1_B_TIE", "1_A_BANKER"],
    ]
    
    # ===== TEST 1: Performance DIFF_RENYI (référence native pure) =====
    print("\n📊 TEST 1: Performance DIFF_RENYI (référence native pure)")
    
    start_time = time.time()
    for i in range(1000):  # Test plus intensif
        for sequence in sequences_test:
            result_renyi = analyseur.entropie_renyi_collision(sequence)
    end_time = time.time()
    
    temps_renyi = end_time - start_time
    nb_calculs_renyi = 1000 * len(sequences_test)
    temps_par_calcul_renyi = temps_renyi / nb_calculs_renyi
    
    print(f"✅ DIFF_RENYI: {temps_renyi:.6f}s pour {nb_calculs_renyi:,} calculs")
    print(f"   → {temps_par_calcul_renyi:.8f}s par calcul")
    
    # ===== TEST 2: Performance DIFF_TOPO (intégration native pure) =====
    print("\n📊 TEST 2: Performance DIFF_TOPO (intégration native pure)")
    
    start_time = time.time()
    for i in range(1000):  # Test plus intensif
        for sequence in sequences_test:
            result_topo = analyseur.entropie_topologique_index5(sequence)
    end_time = time.time()
    
    temps_topo = end_time - start_time
    nb_calculs_topo = 1000 * len(sequences_test)
    temps_par_calcul_topo = temps_topo / nb_calculs_topo
    
    print(f"✅ DIFF_TOPO: {temps_topo:.6f}s pour {nb_calculs_topo:,} calculs")
    print(f"   → {temps_par_calcul_topo:.8f}s par calcul")
    
    # ===== ANALYSE COMPARATIVE FINALE =====
    print("\n🔍 ANALYSE COMPARATIVE FINALE")
    print("-" * 50)
    
    if temps_par_calcul_renyi > 0:
        overhead = ((temps_par_calcul_topo - temps_par_calcul_renyi) / temps_par_calcul_renyi) * 100
        ratio_performance = temps_par_calcul_topo / temps_par_calcul_renyi
    else:
        overhead = 0
        ratio_performance = 1
    
    print(f"📈 Overhead TOPO vs RENYI: {overhead:.1f}%")
    print(f"📊 Ratio performance: {ratio_performance:.2f}x")
    print(f"🎯 Performance TOPO: {(1/ratio_performance)*100:.1f}% de RENYI")
    
    # ===== VALIDATION FINALE =====
    print("\n✅ VALIDATION FINALE")
    print("-" * 30)
    
    # Critères de succès stricts
    success_criteria = {
        "Performance excellente": overhead < 100,  # Moins de 100% d'overhead
        "Performance acceptable": overhead < 200,  # Moins de 200% d'overhead
        "Temps ultra-rapides": temps_par_calcul_topo < 0.00001,  # Moins de 10μs par calcul
        "Résultats cohérents": result_renyi > 0 and result_topo > 0,
        "Élimination rechargements": overhead < 500  # Bien moins que les 8485% d'avant
    }
    
    all_passed = True
    for criterion, passed in success_criteria.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {criterion}")
        if not passed:
            all_passed = False
    
    # ===== COMPARAISON HISTORIQUE =====
    print("\n📈 COMPARAISON HISTORIQUE")
    print("-" * 40)
    print("🔴 AVANT (Délégation): 8485% overhead")
    print("🟡 APRÈS (Intégration): 33.9% overhead")
    print(f"🟢 FINAL (Pure): {overhead:.1f}% overhead")
    
    amelioration_vs_delegation = 8485 - overhead
    print(f"🚀 Amélioration totale: -{amelioration_vs_delegation:.1f}%")
    
    # ===== RÉSUMÉ FINAL =====
    print("\n" + "=" * 70)
    if overhead < 100:
        print("🎯 SUCCÈS EXCEPTIONNEL: Performance native pure optimisée!")
        print(f"   Performance: {overhead:.1f}% overhead vs DIFF_RENYI")
        print("   ✅ Objectif dépassé: Performance quasi-identique")
        grade = "A+"
    elif overhead < 200:
        print("🎯 SUCCÈS: Performance native excellente!")
        print(f"   Performance: {overhead:.1f}% overhead vs DIFF_RENYI")
        print("   ✅ Objectif atteint: Performance native équivalente")
        grade = "A"
    else:
        print("⚠️  ATTENTION: Performance acceptable mais perfectible")
        print(f"   Performance: {overhead:.1f}% overhead vs DIFF_RENYI")
        print("   🔄 Optimisation supplémentaire possible")
        grade = "B"
    
    print(f"   🏆 Grade final: {grade}")
    
    return all_passed, overhead, grade

if __name__ == "__main__":
    success, overhead, grade = test_performance_finale()
    
    print(f"\n📊 RÉSULTAT FINAL:")
    print(f"   Succès: {success}")
    print(f"   Overhead: {overhead:.1f}%")
    print(f"   Grade: {grade}")
    
    # Code de sortie
    sys.exit(0 if success else 1)
