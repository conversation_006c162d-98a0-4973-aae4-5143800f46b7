SYNTHÈSE DE L'EXTRACTION DES MÉTHODES
==================================================

Date d'extraction: 2025-06-27 23:33:47
Fichier source: analyseur_transitions_index5.py

RÉSUMÉ:
- Classes extraites: 3
- Méthodes totales: 48

DÉTAIL PAR CLASSE:
------------------------------
AnalyseurEvolutionEntropique: 15 méthodes
  - __init__
  - analyser_toutes_parties_entropiques
  - analyser_partie_entropique
  - _traiter_chunk_parties
  - _calculer_stats_partie
  - _calculer_analyses_globales
  - generer_rapport_evolution_entropique
  - _ecrire_analyses_globales
  - _generer_signatures_integrees
  - initialiser_bases_signatures
  - _calculer_entropie_shannon
  - _classifier_ratio
  - _compter_categories
  - _analyser_distribution
  - _identifier_zones_predictibles

AnalyseurEvolutionRatios: 20 méthodes
  - __init__
  - analyser_evolution_toutes_parties
  - _analyser_evolution_partie
  - _analyser_tendance
  - _detecter_oscillations
  - _analyser_convergence
  - _extraire_index3_depuis_index5
  - _calculer_variations_ratios
  - _calculer_patterns_soe
  - _calculer_correlation
  - _classifier_pattern_evolution
  - _identifier_patterns_evolution
  - _calculer_statistiques_evolution
  - generer_rapport_evolution_ratios
  - generer_rapport_evolution_complete
  - _ecrire_resume_executif
  - _ecrire_patterns_evolution
  - _ecrire_statistiques_globales
  - _ecrire_analyses_detaillees
  - _ecrire_conclusions

AnalyseurTransitionsIndex5: 13 méthodes
  - __init__
  - _charger_avec_cache_ultra_optimise
  - _detecter_mode_chargement
  - _charger_sans_cache
  - _get_cache_key
  - _charger_depuis_cache
  - _parser_et_cacher
  - _parser_avec_ijson_optimise
  - _parser_avec_ijson_optimise_sans_cache
  - _parser_avec_mmap_orjson
  - _parser_avec_orjson
  - _parser_avec_orjson_sans_cache
  - _creer_cache_optimise

