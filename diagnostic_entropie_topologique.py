#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DIAGNOSTIC ENTROPIE TOPOLOGIQUE
==============================
Analyse le problème des valeurs identiques dans l'entropie topologique
"""

import json
import math
from collections import Counter
from analyse_complete_avec_diff import AnalyseurMetriqueGenerique, detecter_dataset_le_plus_recent

def analyser_entropie_topologique():
    """Diagnostic de l'entropie topologique"""
    print("🔬 DIAGNOSTIC ENTROPIE TOPOLOGIQUE")
    print("=" * 50)
    
    analyseur = AnalyseurMetriqueGenerique()
    
    # 1. TESTER LA MÉTHODE ACTUELLE
    print("\n📊 1. TEST MÉTHODE ACTUELLE")
    print("-" * 30)
    
    # Séquences de test très différentes
    sequences_test = [
        # Séquence uniforme (faible entropie attendue)
        ['1_B_PLAYER', '1_B_PLAYER', '1_B_PLAYER', '1_B_PLAYER'],
        
        # Séquence alternée (entropie moyenne)
        ['1_B_PLAYER', '0_A_BANKER', '1_B_PLAYER', '0_A_BANKER'],
        
        # Séquence très variée (haute entropie attendue)
        ['1_B_PLAYER', '0_C_TIE', '1_A_BANKER', '0_B_PLAYER'],
        
        # Séquence du dataset réel
        ['1_B_TIE', '1_B_PLAYER', '1_C_BANKER', '0_B_PLAYER']
    ]
    
    print("🧪 Test avec méthode actuelle:")
    for i, seq in enumerate(sequences_test):
        try:
            entropie = analyseur.entropie_topologique_index5(seq)
            print(f"   Séq {i+1}: {entropie:.6f} - {seq}")
        except Exception as e:
            print(f"   Séq {i+1}: ERREUR - {e}")
    
    # 2. CALCULER ENTROPIE SHANNON POUR COMPARAISON
    print("\n📊 2. COMPARAISON AVEC ENTROPIE SHANNON")
    print("-" * 40)
    
    def entropie_shannon(sequence):
        """Calcule l'entropie de Shannon d'une séquence"""
        if not sequence:
            return 0.0
        
        # Compter les occurrences
        compteur = Counter(sequence)
        total = len(sequence)
        
        # Calculer l'entropie
        entropie = 0.0
        for count in compteur.values():
            if count > 0:
                p = count / total
                entropie -= p * math.log2(p)
        
        return entropie
    
    print("🧮 Comparaison Shannon vs Topologique:")
    for i, seq in enumerate(sequences_test):
        try:
            shannon = entropie_shannon(seq)
            topo = analyseur.entropie_topologique_index5(seq)
            print(f"   Séq {i+1}:")
            print(f"     Shannon: {shannon:.6f}")
            print(f"     Topo:    {topo:.6f}")
            print(f"     Diff:    {abs(shannon - topo):.6f}")
        except Exception as e:
            print(f"   Séq {i+1}: ERREUR - {e}")
    
    # 3. ANALYSER VRAIES DONNÉES DU DATASET
    print("\n📊 3. ANALYSE VRAIES DONNÉES DATASET")
    print("-" * 40)
    
    # Charger le dataset
    dataset_path, _ = detecter_dataset_le_plus_recent()
    with open(dataset_path, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    # Extraire quelques séquences réelles
    partie = dataset['parties'][0]  # Première partie
    mains = partie.get('mains', [])
    
    # Construire la séquence INDEX5 (en sautant la main 0 qui est vide)
    sequence_index5 = []
    for main in mains[1:]:  # Commencer à main 1
        index5 = main.get('index5_combined', '')
        if index5:
            sequence_index5.append(index5)
    
    print(f"📄 Partie {partie.get('partie_number', 1)}: {len(sequence_index5)} mains")
    print(f"🔍 Premiers INDEX5: {sequence_index5[:10]}")
    
    # Analyser différentes sous-séquences
    if len(sequence_index5) >= 8:
        print("\n🧪 Test sous-séquences réelles:")
        
        for start in range(0, min(4, len(sequence_index5) - 4)):
            seq_4 = sequence_index5[start:start+4]
            seq_5 = sequence_index5[start:start+5] if start+5 <= len(sequence_index5) else None
            
            shannon_4 = entropie_shannon(seq_4)
            topo_4 = analyseur.entropie_topologique_index5(seq_4)
            
            print(f"   Position {start+1}-{start+4}:")
            print(f"     Séq: {seq_4}")
            print(f"     Shannon: {shannon_4:.6f}")
            print(f"     Topo:    {topo_4:.6f}")
            
            if seq_5:
                shannon_5 = entropie_shannon(seq_5)
                topo_5 = analyseur.entropie_topologique_index5(seq_5)
                print(f"   Position {start+1}-{start+5}:")
                print(f"     Séq: {seq_5}")
                print(f"     Shannon: {shannon_5:.6f}")
                print(f"     Topo:    {topo_5:.6f}")
    
    # 4. DIAGNOSTIC DU PROBLÈME
    print("\n🚨 4. DIAGNOSTIC DU PROBLÈME")
    print("-" * 30)
    
    # Vérifier si toutes les valeurs sont identiques
    valeurs_topo = []
    for seq in sequences_test:
        try:
            val = analyseur.entropie_topologique_index5(seq)
            valeurs_topo.append(val)
        except:
            continue
    
    if valeurs_topo:
        valeurs_uniques = set(valeurs_topo)
        print(f"📊 Valeurs topologiques trouvées: {len(valeurs_uniques)} uniques sur {len(valeurs_topo)} tests")
        print(f"🔍 Valeurs: {sorted(valeurs_uniques)}")
        
        if len(valeurs_uniques) == 1:
            print("❌ PROBLÈME CONFIRMÉ: Toutes les séquences donnent la même valeur topologique")
            print("💡 CAUSE PROBABLE: Formule topologique incorrecte ou non-discriminante")
        elif len(valeurs_uniques) < len(valeurs_topo) / 2:
            print("⚠️ PROBLÈME PARTIEL: Peu de variation dans les valeurs topologiques")
        else:
            print("✅ OK: Variation suffisante dans les valeurs topologiques")
    
    return True

if __name__ == "__main__":
    analyser_entropie_topologique()
