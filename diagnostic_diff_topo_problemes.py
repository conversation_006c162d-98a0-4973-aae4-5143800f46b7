#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DIAGNOSTIC DES PROBLÈMES DIFF_TOPO
=================================
Identifie et corrige les problèmes dans les calculs DIFF_TOPO
"""

import json
from analyse_complete_avec_diff import AnalyseurMetriqueGenerique, detecter_dataset_le_plus_recent

def diagnostic_diff_topo():
    """Diagnostic complet des problèmes DIFF_TOPO"""
    print("🚨 DIAGNOSTIC DES PROBLÈMES DIFF_TOPO")
    print("=" * 50)
    
    analyseur = AnalyseurMetriqueGenerique()
    
    # 1. PROBLÈME: SIGNATURES IDENTIQUES
    print("\n🔍 1. DIAGNOSTIC SIGNATURES IDENTIQUES")
    print("-" * 40)
    
    # Tester différentes séquences
    sequences_test = [
        ['0_A_BANKER', '0_A_BANKER', '0_A_BANKER', '0_A_BANKER'],
        ['1_B_PLAYER', '1_C_BANKER', '0_B_PLAYER', '0_B_BANKER'],
        ['0_C_TIE', '1_A_PLAYER', '0_B_TIE', '1_C_BANKER'],
        ['1_A_TIE', '0_C_PLAYER', '1_B_BANKER', '0_A_PLAYER']
    ]
    
    print("🧪 Test de différentes séquences L4:")
    for i, seq in enumerate(sequences_test):
        try:
            signature = analyseur.entropie_topologique_index5(seq)
            print(f"   Séq {i+1}: {seq[:2]}...{seq[-2:]} → {signature:.6f}")
        except Exception as e:
            print(f"   Séq {i+1}: ERREUR - {e}")
    
    # 2. PROBLÈME: FORMULE TOPOLOGIQUE
    print("\n🔍 2. DIAGNOSTIC FORMULE TOPOLOGIQUE")
    print("-" * 40)
    
    # Vérifier la méthode entropie_topologique_index5
    print("🧮 Vérification de la formule topologique:")
    print("   Formule théorique: h_top(T) = lim_n→∞ (1/n) log s_n(ε,T)")
    print("   Implémentation actuelle: ?")
    
    # Tester avec des séquences très différentes
    seq_uniforme = ['0_A_BANKER'] * 4
    seq_aleatoire = ['0_A_BANKER', '1_B_PLAYER', '0_C_TIE', '1_A_PLAYER']
    
    try:
        sig_uniforme = analyseur.entropie_topologique_index5(seq_uniforme)
        sig_aleatoire = analyseur.entropie_topologique_index5(seq_aleatoire)
        
        print(f"   Séquence uniforme: {sig_uniforme:.6f}")
        print(f"   Séquence aléatoire: {sig_aleatoire:.6f}")
        print(f"   Différence: {abs(sig_uniforme - sig_aleatoire):.6f}")
        
        if abs(sig_uniforme - sig_aleatoire) < 0.001:
            print("   ❌ PROBLÈME: Signatures quasi-identiques pour séquences très différentes")
        else:
            print("   ✅ OK: Signatures différentes")
            
    except Exception as e:
        print(f"   ❌ ERREUR: {e}")
    
    # 3. PROBLÈME: CALCUL DIFF_TOPO
    print("\n🔍 3. DIAGNOSTIC CALCUL DIFF_TOPO")
    print("-" * 40)
    
    # Charger une vraie séquence du dataset
    dataset_path, _ = detecter_dataset_le_plus_recent()
    with open(dataset_path, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    partie = dataset['parties'][0]
    mains = partie.get('mains', [])
    sequence_index5 = [main.get('index5_combined', '') for main in mains if main.get('index5_combined')]
    
    if len(sequence_index5) >= 5:
        print(f"📊 Test avec vraie séquence (partie 1, {len(sequence_index5)} mains):")
        
        # Tester plusieurs positions
        for position in range(4, min(8, len(sequence_index5))):
            try:
                seq_l4 = sequence_index5[position-3:position+1]
                seq_l5 = sequence_index5[position-4:position+1]
                
                # Calculer manuellement
                sig_l4 = analyseur.entropie_topologique_index5(seq_l4)
                sig_l5 = analyseur.entropie_topologique_index5(seq_l5)
                
                print(f"   Position {position+1}:")
                print(f"     L4: {seq_l4}")
                print(f"     L5: {seq_l5}")
                print(f"     Sig L4: {sig_l4:.6f}")
                print(f"     Sig L5: {sig_l5:.6f}")
                
                # Calculer DIFF_TOPO manuellement
                if sig_l4 > 0 and sig_l5 > 0:
                    ratio_l4 = sig_l4 / sig_l4  # Devrait être 1.0
                    ratio_l5 = sig_l5 / sig_l4  # Ratio réel
                    diff_topo = abs(ratio_l4 - ratio_l5)
                    print(f"     Ratio L4: {ratio_l4:.6f}")
                    print(f"     Ratio L5: {ratio_l5:.6f}")
                    print(f"     DIFF_TOPO: {diff_topo:.6f}")
                
            except Exception as e:
                print(f"     ERREUR position {position+1}: {e}")
    
    # 4. RECOMMANDATIONS
    print("\n💡 4. RECOMMANDATIONS DE CORRECTION")
    print("-" * 40)
    print("🔧 Problèmes identifiés:")
    print("   1. Signatures topologiques peu discriminantes")
    print("   2. Formule topologique possiblement incorrecte")
    print("   3. Erreur de formatage dans l'affichage")
    print("   4. Calculs DIFF_TOPO non-variables")
    
    print("\n🎯 Solutions proposées:")
    print("   1. Réviser la formule d'entropie topologique")
    print("   2. Utiliser une approche plus discriminante")
    print("   3. Corriger les erreurs de formatage")
    print("   4. Vérifier les calculs de ratios")
    
    return True

if __name__ == "__main__":
    diagnostic_diff_topo()
