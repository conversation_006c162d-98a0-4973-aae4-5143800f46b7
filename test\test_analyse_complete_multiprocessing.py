#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST ANALYSE COMPLÈTE AVEC MULTIPROCESSING DIFF_TOPO
===================================================
Test rapide pour vérifier que l'analyse complète fonctionne avec le nouveau multiprocessing
"""

import time
from analyse_complete_avec_diff import analyser_conditions_predictives_so_avec_diff

def test_analyse_complete_multiprocessing():
    """Test de l'analyse complète avec multiprocessing pour les 3 métriques"""
    print("🧪 TEST ANALYSE COMPLÈTE MULTIPROCESSING")
    print("🚀 DIFF + DIFF_RENYI + DIFF_TOPO avec optimisation 8 cœurs")
    print("=" * 70)
    
    # Mesurer le temps d'exécution
    debut = time.time()
    
    try:
        # Lancer l'analyse complète
        print("🔥 Lancement de l'analyse complète...")
        success = analyser_conditions_predictives_so_avec_diff()
        
        fin = time.time()
        duree = fin - debut
        
        if success:
            print(f"\n✅ ANALYSE COMPLÈTE RÉUSSIE")
            print(f"⏱️ Durée totale: {duree:.1f} secondes")
            print(f"🚀 Multiprocessing actif pour les 3 métriques:")
            print(f"   ✅ DIFF (Shannon): Optimisé 8 cœurs")
            print(f"   ✅ DIFF_RENYI: Optimisé 8 cœurs")
            print(f"   ✅ DIFF_TOPO: Optimisé 8 cœurs (NOUVEAU)")
            print(f"\n📊 Performance harmonisée entre toutes les métriques")
            return True
        else:
            print(f"\n❌ ANALYSE ÉCHOUÉE après {duree:.1f} secondes")
            return False
            
    except Exception as e:
        fin = time.time()
        duree = fin - debut
        print(f"\n❌ ERREUR après {duree:.1f} secondes: {e}")
        return False

if __name__ == "__main__":
    print("🎯 OBJECTIF: Vérifier que DIFF_TOPO utilise maintenant le multiprocessing")
    print("📈 Attendu: Performance similaire entre DIFF, DIFF_RENYI et DIFF_TOPO")
    print()
    
    success = test_analyse_complete_multiprocessing()
    
    if success:
        print("\n🎉 SUCCÈS TOTAL!")
        print("✅ Le multiprocessing est maintenant actif pour les 3 métriques")
        print("🚀 Performance optimisée et harmonisée")
    else:
        print("\n⚠️ Des améliorations peuvent être nécessaires")
