#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TRAÇAGE DES CALCULS DIFF_TOPO
============================
Trace étape par étape les calculs pour comprendre les valeurs
"""

import json
from analyse_complete_avec_diff import AnalyseurMetriqueGenerique, detecter_dataset_le_plus_recent

def tracer_calculs_diff_topo():
    """Trace les calculs DIFF_TOPO étape par étape"""
    print("🔍 TRAÇAGE DES CALCULS DIFF_TOPO")
    print("=" * 50)
    
    # Initialiser l'analyseur
    analyseur = AnalyseurMetriqueGenerique()
    
    # Charger le dataset
    dataset_path, _ = detecter_dataset_le_plus_recent()
    with open(dataset_path, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    # Prendre la première partie
    partie = dataset['parties'][0]
    mains = partie.get('mains', [])
    
    # Construire la séquence INDEX5 (en sautant la main 0 qui est vide)
    sequence_index5 = []
    for main in mains[1:]:  # Commencer à main 1
        index5 = main.get('index5_combined', '')
        if index5:
            sequence_index5.append(index5)
    
    print(f"📄 Partie {partie.get('partie_number', 1)}: {len(sequence_index5)} mains")
    print(f"🔍 Séquence INDEX5: {sequence_index5[:10]}...")
    
    # Analyser la main 9 (position 8) qui donne les valeurs de l'exemple
    position = 8  # Main 9 (index 8)
    
    print(f"\n🎯 ANALYSE MAIN {position+1} (position {position}):")
    print("-" * 50)
    
    # ÉTAPE 1: Extraction des séquences
    seq_L4 = sequence_index5[position-3:position+1]
    seq_L5 = sequence_index5[position-4:position+1]
    seq_globale = sequence_index5[1:position+1]  # Skip main 0
    
    print(f"📊 ÉTAPE 1: EXTRACTION DES SÉQUENCES")
    print(f"   seq_L4 (pos {position-3} à {position}): {seq_L4}")
    print(f"   seq_L5 (pos {position-4} à {position}): {seq_L5}")
    print(f"   seq_globale (pos 1 à {position}): {seq_globale}")
    print(f"   Longueurs: L4={len(seq_L4)}, L5={len(seq_L5)}, globale={len(seq_globale)}")
    
    # ÉTAPE 2: Récupération des signatures pré-calculées
    print(f"\n📊 ÉTAPE 2: SIGNATURES PRÉ-CALCULÉES")
    
    # S'assurer que les bases de signatures existent
    if 'TOPO' not in analyseur.bases_signatures_4:
        analyseur.generer_signatures_topo_longueur_4()
    if 'TOPO' not in analyseur.bases_signatures_5:
        analyseur.generer_signatures_topo_longueur_5()
    
    seq_L4_tuple = tuple(seq_L4)
    seq_L5_tuple = tuple(seq_L5)
    
    signature_L4_topo = analyseur.bases_signatures_4['TOPO'].get(seq_L4_tuple, 0.0)
    signature_L5_topo = analyseur.bases_signatures_5['TOPO'].get(seq_L5_tuple, 0.0)
    
    print(f"   Signature L4 (pré-calc): {signature_L4_topo:.6f}")
    print(f"   Signature L5 (pré-calc): {signature_L5_topo:.6f}")
    
    # Vérifier comment ces signatures sont calculées
    print(f"\n🔬 VÉRIFICATION CALCUL SIGNATURES:")
    sig_L4_direct = analyseur.entropie_topologique_index5(seq_L4)
    sig_L5_direct = analyseur.entropie_topologique_index5(seq_L5)
    print(f"   Signature L4 (direct): {sig_L4_direct:.6f}")
    print(f"   Signature L5 (direct): {sig_L5_direct:.6f}")
    print(f"   Cohérence L4: {'✅' if abs(signature_L4_topo - sig_L4_direct) < 1e-6 else '❌'}")
    print(f"   Cohérence L5: {'✅' if abs(signature_L5_topo - sig_L5_direct) < 1e-6 else '❌'}")
    
    # ÉTAPE 3: Calcul de la signature globale
    print(f"\n📊 ÉTAPE 3: SIGNATURE GLOBALE")
    signature_globale_topo = analyseur.entropie_topologique_index5(seq_globale)
    print(f"   Signature globale (à la volée): {signature_globale_topo:.6f}")
    print(f"   Séquence globale: {seq_globale}")
    
    # ÉTAPE 4: Calcul des ratios
    print(f"\n📊 ÉTAPE 4: CALCUL DES RATIOS")
    ratio_L4_topo = signature_L4_topo / signature_globale_topo if signature_globale_topo > 0 else 0
    ratio_L5_topo = signature_L5_topo / signature_globale_topo if signature_globale_topo > 0 else 0
    
    print(f"   Ratio L4 = signature_L4 / signature_globale")
    print(f"   Ratio L4 = {signature_L4_topo:.6f} / {signature_globale_topo:.6f} = {ratio_L4_topo:.6f}")
    print(f"   Ratio L5 = signature_L5 / signature_globale")
    print(f"   Ratio L5 = {signature_L5_topo:.6f} / {signature_globale_topo:.6f} = {ratio_L5_topo:.6f}")
    
    # ÉTAPE 5: Calcul DIFF_TOPO
    print(f"\n📊 ÉTAPE 5: CALCUL DIFF_TOPO")
    diff_topo = abs(ratio_L4_topo - ratio_L5_topo)
    print(f"   DIFF_TOPO = |ratio_L4 - ratio_L5|")
    print(f"   DIFF_TOPO = |{ratio_L4_topo:.6f} - {ratio_L5_topo:.6f}|")
    print(f"   DIFF_TOPO = |{ratio_L4_topo - ratio_L5_topo:.6f}|")
    print(f"   DIFF_TOPO = {diff_topo:.6f}")
    
    # COMPARAISON AVEC LES VALEURS ATTENDUES
    print(f"\n🎯 COMPARAISON AVEC LES VALEURS ATTENDUES:")
    print(f"   Signature L4: Calculé={signature_L4_topo:.6f}, Attendu=0.500000")
    print(f"   Signature L5: Calculé={signature_L5_topo:.6f}, Attendu=0.464386")
    print(f"   Ratio L4:     Calculé={ratio_L4_topo:.6f}, Attendu=1.333333")
    print(f"   Ratio L5:     Calculé={ratio_L5_topo:.6f}, Attendu=1.238362")
    print(f"   DIFF_TOPO:    Calculé={diff_topo:.6f}, Attendu=0.094972")
    
    # ANALYSE DE LA SIGNATURE GLOBALE
    print(f"\n🔬 ANALYSE SIGNATURE GLOBALE:")
    print(f"   Signature globale: {signature_globale_topo:.6f}")
    print(f"   Séquence globale: {seq_globale}")
    print(f"   Longueur: {len(seq_globale)}")
    
    # Calculer manuellement l'entropie topologique de la séquence globale
    print(f"\n🧮 CALCUL MANUEL ENTROPIE TOPOLOGIQUE:")
    print(f"   Séquence: {seq_globale}")
    
    # Vérifier si la signature globale change selon la position
    print(f"\n📈 ÉVOLUTION SIGNATURE GLOBALE:")
    for pos in range(4, min(10, len(sequence_index5))):
        seq_glob_pos = sequence_index5[1:pos+1]
        sig_glob_pos = analyseur.entropie_topologique_index5(seq_glob_pos)
        print(f"   Position {pos+1}: longueur={len(seq_glob_pos)}, signature={sig_glob_pos:.6f}")
    
    return True

if __name__ == "__main__":
    tracer_calculs_diff_topo()
