#!/usr/bin/env python3
"""
TEST GÉNÉRATION SÉQUENCES BCT NATIVES
====================================

Calcule exactement combien de séquences sont générées par la logique BCT native
"""

import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analyse_complete_avec_diff import AnalyseurMetriqueGenerique

def test_generation_sequences():
    """Test de génération des séquences BCT natives"""
    
    print("🔍 TEST GÉNÉRATION SÉQUENCES BCT NATIVES")
    print("=" * 50)
    
    # Initialiser l'analyseur
    analyseur = AnalyseurMetriqueGenerique()
    
    # ===== CALCUL THÉORIQUE =====
    print("\n📊 CALCUL THÉORIQUE")
    print("-" * 30)
    
    # Valeurs INDEX5 possibles
    index1_values = ['0', '1']  # 2 valeurs
    index2_values = ['A', 'B', 'C']  # 3 valeurs  
    index3_values = ['BANKER', 'PLAYER', 'TIE']  # 3 valeurs
    
    total_index5 = len(index1_values) * len(index2_values) * len(index3_values)
    print(f"📈 Total INDEX5 possibles: {total_index5}")
    
    # Pour chaque INDEX5, combien de transitions valides ?
    print("\n🔄 TRANSITIONS VALIDES PAR INDEX5:")
    
    exemple_transitions = {}
    for i1 in index1_values:
        for i2 in index2_values:
            for i3 in index3_values:
                index5_source = f"{i1}_{i2}_{i3}"
                transitions = analyseur._generer_transitions_valides_bct(index5_source)
                exemple_transitions[index5_source] = len(transitions)
                if len(exemple_transitions) <= 6:  # Afficher quelques exemples
                    print(f"   {index5_source} → {len(transitions)} transitions")
    
    transitions_moyennes = sum(exemple_transitions.values()) / len(exemple_transitions)
    print(f"   📊 Moyenne: {transitions_moyennes:.1f} transitions par INDEX5")
    
    # ===== CALCUL SÉQUENCES L4 =====
    print("\n📊 SÉQUENCES DE LONGUEUR 4")
    print("-" * 35)
    
    # Calcul théorique approximatif
    sequences_l4_theorique = total_index5 * (transitions_moyennes ** 3)
    print(f"📈 Estimation théorique L4: {sequences_l4_theorique:,.0f}")
    
    # Génération réelle pour TOPO L4
    print("🔄 Génération réelle TOPO L4...")
    signatures_topo_4 = analyseur.generer_signatures_topo_longueur_4()
    sequences_l4_reelles = len(signatures_topo_4)
    print(f"✅ Séquences L4 générées: {sequences_l4_reelles:,}")
    
    # ===== CALCUL SÉQUENCES L5 =====
    print("\n📊 SÉQUENCES DE LONGUEUR 5")
    print("-" * 35)
    
    # Calcul théorique approximatif
    sequences_l5_theorique = total_index5 * (transitions_moyennes ** 4)
    print(f"📈 Estimation théorique L5: {sequences_l5_theorique:,.0f}")
    
    # Génération réelle pour TOPO L5
    print("🔄 Génération réelle TOPO L5...")
    signatures_topo_5 = analyseur.generer_signatures_topo_longueur_5()
    sequences_l5_reelles = len(signatures_topo_5)
    print(f"✅ Séquences L5 générées: {sequences_l5_reelles:,}")
    
    # ===== ANALYSE DÉTAILLÉE =====
    print("\n🔍 ANALYSE DÉTAILLÉE")
    print("-" * 30)
    
    print("📋 RÈGLES BCT APPLIQUÉES:")
    print("   • C → Alternance: 0↔1")
    print("   • A,B → Conservation: 0→0, 1→1")
    
    print(f"\n📊 RÉSULTATS:")
    print(f"   • INDEX5 de départ: {total_index5}")
    print(f"   • Transitions par INDEX5: {transitions_moyennes:.1f}")
    print(f"   • Séquences L4: {sequences_l4_reelles:,}")
    print(f"   • Séquences L5: {sequences_l5_reelles:,}")
    
    # Vérification cohérence
    ratio_l5_l4 = sequences_l5_reelles / sequences_l4_reelles if sequences_l4_reelles > 0 else 0
    print(f"   • Ratio L5/L4: {ratio_l5_l4:.1f}x")
    print(f"   • Attendu: ~{transitions_moyennes:.1f}x")
    
    # ===== EXEMPLES DE SÉQUENCES =====
    print("\n📝 EXEMPLES DE SÉQUENCES GÉNÉRÉES")
    print("-" * 40)
    
    print("🔸 Quelques séquences L4:")
    for i, (sequence, signature) in enumerate(list(signatures_topo_4.items())[:3]):
        print(f"   {i+1}. {' → '.join(sequence)} (TOPO: {signature:.6f})")
    
    print("🔸 Quelques séquences L5:")
    for i, (sequence, signature) in enumerate(list(signatures_topo_5.items())[:3]):
        print(f"   {i+1}. {' → '.join(sequence)} (TOPO: {signature:.6f})")
    
    # ===== VALIDATION BCT =====
    print("\n✅ VALIDATION RÈGLES BCT")
    print("-" * 35)
    
    # Vérifier quelques séquences
    sequences_valides = 0
    sequences_testees = 0
    
    for sequence, _ in list(signatures_topo_4.items())[:100]:  # Tester 100 séquences
        sequences_testees += 1
        valide = True
        
        for i in range(len(sequence) - 1):
            source = sequence[i]
            cible = sequence[i + 1]
            
            # Vérifier si la transition respecte les règles BCT
            transitions_valides = analyseur._generer_transitions_valides_bct(source)
            if cible not in transitions_valides:
                valide = False
                break
        
        if valide:
            sequences_valides += 1
    
    pourcentage_valide = (sequences_valides / sequences_testees) * 100 if sequences_testees > 0 else 0
    print(f"📊 Séquences testées: {sequences_testees}")
    print(f"✅ Séquences valides BCT: {sequences_valides} ({pourcentage_valide:.1f}%)")
    
    # ===== RÉSUMÉ =====
    print("\n" + "=" * 50)
    print("🎯 RÉSUMÉ GÉNÉRATION BCT NATIVE")
    print(f"   ✅ Toutes les séquences L4 générées: {sequences_l4_reelles:,}")
    print(f"   ✅ Toutes les séquences L5 générées: {sequences_l5_reelles:,}")
    print(f"   ✅ Respect des règles BCT: {pourcentage_valide:.1f}%")
    print(f"   ✅ Logique native pure: Sans dépendances externes")
    
    return sequences_l4_reelles, sequences_l5_reelles, pourcentage_valide

if __name__ == "__main__":
    l4, l5, validite = test_generation_sequences()
    
    print(f"\n📊 RÉSULTAT FINAL:")
    print(f"   L4: {l4:,} séquences")
    print(f"   L5: {l5:,} séquences") 
    print(f"   Validité BCT: {validite:.1f}%")
