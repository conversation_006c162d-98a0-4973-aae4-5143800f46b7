# 🟢 EXERCICES NIVEAU DÉBUTANT
## Pratique des Concepts Fondamentaux d'Entropie

### 🎯 Objectifs des Exercices
- Maîtriser le calcul de l'entropie de Shannon
- Comprendre l'entropie conditionnelle et l'information mutuelle
- Appliquer les concepts à des situations concrètes
- Développer l'intuition mathématique

---

## 📚 SÉRIE 1 : ENTROPIE DE SHANNON

### Exercice 1.1 : Calculs de Base

**Calculez l'entropie de Shannon pour chaque distribution :**

a) Pièce équilibrée : p = (0.5, 0.5)
b) Dé équilibré : p = (1/6, 1/6, 1/6, 1/6, 1/6, 1/6)
c) Distribution biaisée : p = (0.8, 0.2)
d) Distribution certaine : p = (1.0, 0.0)

**Solutions :**
```
a) H = -0.5×log₂(0.5) - 0.5×log₂(0.5) = 1.000 bit
b) H = 6×(-(1/6)×log₂(1/6)) = log₂(6) ≈ 2.585 bits
c) H = -0.8×log₂(0.8) - 0.2×log₂(0.2) ≈ 0.722 bit
d) H = -1×log₂(1) - 0×log₂(0) = 0.000 bit
```

### Exercice 1.2 : Comparaison d'Entropies

**Classez ces systèmes par ordre croissant d'entropie :**

1. Lancer d'une pièce truquée (90% pile, 10% face)
2. Choix d'une carte dans un jeu de 32 cartes
3. Prédiction qu'il fera jour demain à midi
4. Lancer d'un dé à 8 faces équilibrées
5. Choix entre 3 options équiprobables

**Réponse :** 3 < 1 < 5 < 4 < 2

### Exercice 1.3 : Optimisation

**Trouvez la valeur de p qui maximise l'entropie de la distribution (p, 1-p).**

**Solution :**
- Fonction à maximiser : H(p) = -p log₂(p) - (1-p) log₂(1-p)
- Dérivée : H'(p) = -log₂(p) + log₂(1-p)
- H'(p) = 0 ⟹ log₂(p) = log₂(1-p) ⟹ p = 1-p ⟹ p = 0.5
- Maximum : H(0.5) = 1 bit

---

## 📊 SÉRIE 2 : ENTROPIE CONDITIONNELLE

### Exercice 2.1 : Météo et Saisons

**Données :**
- Probabilités des saisons : P(Été) = 0.25, P(Hiver) = 0.25, P(Printemps) = 0.25, P(Automne) = 0.25
- Probabilités de pluie :
  - P(Pluie|Été) = 0.1
  - P(Pluie|Hiver) = 0.7
  - P(Pluie|Printemps) = 0.4
  - P(Pluie|Automne) = 0.5

**Questions :**
a) Calculez H(Météo|Saison)
b) Calculez H(Météo)
c) Comparez les résultats

**Solutions :**
```
a) H(Météo|Été) = -0.1×log₂(0.1) - 0.9×log₂(0.9) ≈ 0.469 bit
   H(Météo|Hiver) = -0.7×log₂(0.7) - 0.3×log₂(0.3) ≈ 0.881 bit
   H(Météo|Printemps) = -0.4×log₂(0.4) - 0.6×log₂(0.6) ≈ 0.971 bit
   H(Météo|Automne) = -0.5×log₂(0.5) - 0.5×log₂(0.5) = 1.000 bit
   
   H(Météo|Saison) = 0.25×(0.469 + 0.881 + 0.971 + 1.000) ≈ 0.830 bit

b) P(Pluie) = 0.25×(0.1 + 0.7 + 0.4 + 0.5) = 0.425
   H(Météo) = -0.425×log₂(0.425) - 0.575×log₂(0.575) ≈ 0.985 bit

c) H(Météo|Saison) < H(Météo) : connaître la saison réduit l'incertitude
```

### Exercice 2.2 : Diagnostic Médical

**Contexte :** Un test médical pour détecter une maladie

**Données :**
- P(Malade) = 0.01 (1% de la population)
- P(Test+|Malade) = 0.95 (sensibilité)
- P(Test-|Sain) = 0.98 (spécificité)

**Questions :**
a) Construisez la table de probabilités jointes
b) Calculez H(Maladie|Test)
c) Interprétez le résultat

### Exercice 2.3 : Jeu de Cartes

**Situation :** Prédire la couleur d'une carte sachant sa valeur

**Données :**
- 52 cartes : 13 valeurs × 4 couleurs
- Distribution uniforme

**Questions :**
a) Calculez H(Couleur)
b) Calculez H(Couleur|Valeur)
c) Que pouvez-vous conclure ?

**Solutions :**
```
a) H(Couleur) = log₂(4) = 2 bits
b) H(Couleur|Valeur) = H(Couleur) = 2 bits (indépendance)
c) La valeur n'apporte aucune information sur la couleur
```

---

## 🔗 SÉRIE 3 : INFORMATION MUTUELLE

### Exercice 3.1 : Variables Dépendantes

**Distribution jointe :**
```
     Y=0  Y=1
X=0  0.4  0.1
X=1  0.2  0.3
```

**Questions :**
a) Calculez les distributions marginales
b) Calculez H(X), H(Y), H(X,Y)
c) Calculez I(X;Y)
d) Vérifiez I(X;Y) = H(X) - H(X|Y)

### Exercice 3.2 : Indépendance

**Montrez que si X et Y sont indépendants, alors I(X;Y) = 0**

**Démonstration :**
- Si X ⊥ Y, alors p(x,y) = p(x)p(y)
- I(X;Y) = ∑∑ p(x,y) log₂(p(x,y)/(p(x)p(y)))
- I(X;Y) = ∑∑ p(x)p(y) log₂(p(x)p(y)/(p(x)p(y)))
- I(X;Y) = ∑∑ p(x)p(y) log₂(1) = 0

### Exercice 3.3 : Application Pratique

**Contexte :** Analyse de données de vente

**Variables :**
- X = Âge du client (Jeune/Âgé)
- Y = Produit acheté (A/B)

**Données observées :**
- 1000 clients jeunes : 600 achètent A, 400 achètent B
- 1000 clients âgés : 300 achètent A, 700 achètent B

**Questions :**
a) Construisez la table de probabilités jointes
b) Calculez I(Âge;Produit)
c) Interprétez le résultat pour le marketing

---

## 🎯 SÉRIE 4 : APPLICATIONS PRATIQUES

### Exercice 4.1 : Compression de Texte

**Texte :** "ABRACADABRA"

**Questions :**
a) Calculez la fréquence de chaque lettre
b) Calculez l'entropie du texte
c) Quelle est la longueur minimale théorique de compression ?

**Solution :**
```
Fréquences : A=5/11, B=2/11, R=2/11, C=1/11, D=1/11
H ≈ 2.18 bits par symbole
Longueur minimale ≈ 11 × 2.18 ≈ 24 bits
```

### Exercice 4.2 : Sécurité des Mots de Passe

**Comparez l'entropie de ces mots de passe :**

a) "password" (8 lettres minuscules)
b) "P@ssw0rd" (8 caractères mixtes)
c) "correct horse battery staple" (4 mots courants)

**Analyse :**
- Calculez l'espace des possibilités pour chaque cas
- Déduisez l'entropie en bits
- Recommandez la meilleure stratégie

### Exercice 4.3 : Détection d'Anomalies

**Contexte :** Surveillance réseau

**Données normales :** Trafic avec distribution connue
**Données suspectes :** Trafic avec distribution différente

**Méthode :**
- Utilisez la divergence KL pour détecter les anomalies
- Définissez un seuil de détection
- Évaluez les faux positifs/négatifs

---

## 🧮 SÉRIE 5 : PROBLÈMES DE SYNTHÈSE

### Exercice 5.1 : Système de Communication

**Contexte :** Canal de transmission bruité

**Données :**
- Source : 4 symboles équiprobables
- Canal : probabilité d'erreur 0.1 par symbole
- Récepteur : observe le signal bruité

**Questions :**
a) Calculez H(Source)
b) Calculez H(Réception|Source)
c) Calculez I(Source;Réception)
d) Interprétez en termes de capacité du canal

### Exercice 5.2 : Apprentissage Automatique

**Contexte :** Classification binaire

**Données d'entraînement :**
- 1000 exemples de classe A
- 1000 exemples de classe B
- 3 caractéristiques par exemple

**Objectif :** Sélectionner les caractéristiques les plus informatives

**Méthode :**
- Calculez I(Caractéristique;Classe) pour chaque caractéristique
- Classez par ordre d'importance
- Proposez un algorithme de sélection

### Exercice 5.3 : Analyse de Données Biologiques

**Contexte :** Séquences ADN

**Données :** Séquence de 1000 nucléotides (A, T, G, C)

**Questions :**
a) Calculez l'entropie de la séquence
b) Analysez les dépendances entre positions adjacentes
c) Proposez un modèle de compression adapté

---

## 🔍 SÉRIE 6 : VÉRIFICATION ET VALIDATION

### Exercice 6.1 : Propriétés Mathématiques

**Vérifiez ces propriétés sur des exemples numériques :**

a) H(X,Y) ≤ H(X) + H(Y)
b) I(X;Y) ≥ 0
c) H(Y|X) ≤ H(Y)
d) I(X;Y) = I(Y;X)

### Exercice 6.2 : Cas Limites

**Analysez le comportement dans ces cas extrêmes :**

a) Une probabilité tend vers 0
b) Toutes les probabilités sont égales
c) Une probabilité vaut 1
d) Le nombre d'événements tend vers l'infini

### Exercice 6.3 : Erreurs Courantes

**Identifiez et corrigez ces erreurs :**

a) Calculer log(0) directement
b) Oublier de normaliser les probabilités
c) Confondre entropie et information mutuelle
d) Mal interpréter l'entropie conditionnelle

---

## 📊 BARÈME ET ÉVALUATION

### Critères d'Évaluation

**Calculs (40%)** :
- Exactitude des formules
- Précision numérique
- Gestion des cas particuliers

**Interprétation (30%)** :
- Compréhension conceptuelle
- Analyse des résultats
- Liens avec les applications

**Méthode (20%)** :
- Démarche structurée
- Justification des étapes
- Vérifications

**Communication (10%)** :
- Clarté de la présentation
- Utilisation du vocabulaire technique
- Synthèse des conclusions

### Niveaux de Maîtrise

**Débutant (0-12/20)** : Calculs de base avec aide
**Intermédiaire (13-16/20)** : Maîtrise des concepts principaux
**Avancé (17-20/20)** : Autonomie complète et applications créatives

---

## 🔗 RESSOURCES COMPLÉMENTAIRES

**Pour aller plus loin :**
- [Niveau Intermédiaire](../niveau_intermediaire/README.md)
- [Formulaire Complet](../ressources/formulaire_complet.md)
- [Code Python](../ressources/implementations_python.py)
- [Exercices Interactifs](../ressources/exercices_interactifs.ipynb)

**Outils recommandés :**
- Calculatrice scientifique
- Python avec NumPy/SciPy
- Tableur pour les calculs matriciels
- Logiciel de visualisation

---

*Ces exercices couvrent tous les aspects du niveau débutant et préparent efficacement au niveau intermédiaire.*
